{"program": {"fileNames": ["./node_modules/typescript/lib/lib.es5.d.ts", "./node_modules/typescript/lib/lib.es2015.d.ts", "./node_modules/typescript/lib/lib.es2016.d.ts", "./node_modules/typescript/lib/lib.es2017.d.ts", "./node_modules/typescript/lib/lib.es2018.d.ts", "./node_modules/typescript/lib/lib.es2019.d.ts", "./node_modules/typescript/lib/lib.es2020.d.ts", "./node_modules/typescript/lib/lib.dom.d.ts", "./node_modules/typescript/lib/lib.dom.iterable.d.ts", "./node_modules/typescript/lib/lib.es2015.core.d.ts", "./node_modules/typescript/lib/lib.es2015.collection.d.ts", "./node_modules/typescript/lib/lib.es2015.generator.d.ts", "./node_modules/typescript/lib/lib.es2015.iterable.d.ts", "./node_modules/typescript/lib/lib.es2015.promise.d.ts", "./node_modules/typescript/lib/lib.es2015.proxy.d.ts", "./node_modules/typescript/lib/lib.es2015.reflect.d.ts", "./node_modules/typescript/lib/lib.es2015.symbol.d.ts", "./node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "./node_modules/typescript/lib/lib.es2016.array.include.d.ts", "./node_modules/typescript/lib/lib.es2017.date.d.ts", "./node_modules/typescript/lib/lib.es2017.object.d.ts", "./node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "./node_modules/typescript/lib/lib.es2017.string.d.ts", "./node_modules/typescript/lib/lib.es2017.intl.d.ts", "./node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "./node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "./node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "./node_modules/typescript/lib/lib.es2018.intl.d.ts", "./node_modules/typescript/lib/lib.es2018.promise.d.ts", "./node_modules/typescript/lib/lib.es2018.regexp.d.ts", "./node_modules/typescript/lib/lib.es2019.array.d.ts", "./node_modules/typescript/lib/lib.es2019.object.d.ts", "./node_modules/typescript/lib/lib.es2019.string.d.ts", "./node_modules/typescript/lib/lib.es2019.symbol.d.ts", "./node_modules/typescript/lib/lib.es2019.intl.d.ts", "./node_modules/typescript/lib/lib.es2020.bigint.d.ts", "./node_modules/typescript/lib/lib.es2020.date.d.ts", "./node_modules/typescript/lib/lib.es2020.promise.d.ts", "./node_modules/typescript/lib/lib.es2020.sharedmemory.d.ts", "./node_modules/typescript/lib/lib.es2020.string.d.ts", "./node_modules/typescript/lib/lib.es2020.symbol.wellknown.d.ts", "./node_modules/typescript/lib/lib.es2020.intl.d.ts", "./node_modules/typescript/lib/lib.es2020.number.d.ts", "./node_modules/typescript/lib/lib.decorators.d.ts", "./node_modules/typescript/lib/lib.decorators.legacy.d.ts", "./node_modules/vite/types/hmrpayload.d.ts", "./node_modules/vite/types/customevent.d.ts", "./node_modules/vite/types/hot.d.ts", "./node_modules/vite/types/importglob.d.ts", "./node_modules/vite/types/importmeta.d.ts", "./node_modules/vite/client.d.ts", "./env.d.ts", "./node_modules/@vue/shared/dist/shared.d.ts", "./node_modules/@vue/reactivity/dist/reactivity.d.ts", "./node_modules/@vue/runtime-core/dist/runtime-core.d.ts", "./node_modules/csstype/index.d.ts", "./node_modules/@vue/runtime-dom/dist/runtime-dom.d.ts", "./node_modules/vue/jsx-runtime/index.d.ts", "./node_modules/@babel/types/lib/index.d.ts", "./node_modules/@babel/parser/typings/babel-parser.d.ts", "./node_modules/@vue/compiler-core/dist/compiler-core.d.ts", "./node_modules/@vue/compiler-dom/dist/compiler-dom.d.ts", "./node_modules/vue/dist/vue.d.mts", "./node_modules/vue-demi/lib/index.d.ts", "./node_modules/pinia/dist/pinia.d.ts", "./node_modules/vue-router/dist/vue-router.d.ts", "./src/types/common.ts", "./src/types/ui.ts", "./src/types/assessment.ts", "./src/types/payment.ts", "./src/types/services.ts", "./src/types/index.ts", "./src/stores/ui.ts", "./src/stores/assessment.ts", "./src/router/index.ts", "./src/main.ts", "./src/composables/useswipegesture.ts", "./src/data/questions.ts", "./src/services/storageservice.ts", "./src/services/calculationservice.ts", "./src/services/assessmentservice.ts", "./src/services/paymentservice.ts", "./src/services/index.ts", "./src/services/questionservice.ts", "./src/stores/payment.ts", "./src/stores/user.ts", "./src/stores/index.ts"], "fileInfos": [{"version": "f33e5332b24c3773e930e212cbb8b6867c8ba3ec4492064ea78e55a524d57450", "affectsGlobalScope": true}, "45b7ab580deca34ae9729e97c13cfd999df04416a79116c3bfb483804f85ded4", "26f2f787e82c4222710f3b676b4d83eb5ad0a72fa7b746f03449e7a026ce5073", "9a68c0c07ae2fa71b44384a839b7b8d81662a236d4b9ac30916718f7510b1b2d", "5e1c4c362065a6b95ff952c0eab010f04dcd2c3494e813b493ecfd4fcb9fc0d8", "68d73b4a11549f9c0b7d352d10e91e5dca8faa3322bfb77b661839c42b1ddec7", "5efce4fc3c29ea84e8928f97adec086e3dc876365e0982cc8479a07954a3efd4", {"version": "21e41a76098aa7a191028256e52a726baafd45a925ea5cf0222eb430c96c1d83", "affectsGlobalScope": true}, {"version": "35299ae4a62086698444a5aaee27fc7aa377c68cbb90b441c9ace246ffd05c97", "affectsGlobalScope": true}, {"version": "138fb588d26538783b78d1e3b2c2cc12d55840b97bf5e08bca7f7a174fbe2f17", "affectsGlobalScope": true}, {"version": "dc2df20b1bcdc8c2d34af4926e2c3ab15ffe1160a63e58b7e09833f616efff44", "affectsGlobalScope": true}, {"version": "4443e68b35f3332f753eacc66a04ac1d2053b8b035a0e0ac1d455392b5e243b3", "affectsGlobalScope": true}, {"version": "bc47685641087c015972a3f072480889f0d6c65515f12bd85222f49a98952ed7", "affectsGlobalScope": true}, {"version": "0dc1e7ceda9b8b9b455c3a2d67b0412feab00bd2f66656cd8850e8831b08b537", "affectsGlobalScope": true}, {"version": "ce691fb9e5c64efb9547083e4a34091bcbe5bdb41027e310ebba8f7d96a98671", "affectsGlobalScope": true}, {"version": "8d697a2a929a5fcb38b7a65594020fcef05ec1630804a33748829c5ff53640d0", "affectsGlobalScope": true}, {"version": "4ff2a353abf8a80ee399af572debb8faab2d33ad38c4b4474cff7f26e7653b8d", "affectsGlobalScope": true}, {"version": "93495ff27b8746f55d19fcbcdbaccc99fd95f19d057aed1bd2c0cafe1335fbf0", "affectsGlobalScope": true}, {"version": "6fc23bb8c3965964be8c597310a2878b53a0306edb71d4b5a4dfe760186bcc01", "affectsGlobalScope": true}, {"version": "38f0219c9e23c915ef9790ab1d680440d95419ad264816fa15009a8851e79119", "affectsGlobalScope": true}, {"version": "bb42a7797d996412ecdc5b2787720de477103a0b2e53058569069a0e2bae6c7e", "affectsGlobalScope": true}, {"version": "4738f2420687fd85629c9efb470793bb753709c2379e5f85bc1815d875ceadcd", "affectsGlobalScope": true}, {"version": "2f11ff796926e0832f9ae148008138ad583bd181899ab7dd768a2666700b1893", "affectsGlobalScope": true}, {"version": "4de680d5bb41c17f7f68e0419412ca23c98d5749dcaaea1896172f06435891fc", "affectsGlobalScope": true}, {"version": "9fc46429fbe091ac5ad2608c657201eb68b6f1b8341bd6d670047d32ed0a88fa", "affectsGlobalScope": true}, {"version": "61c37c1de663cf4171e1192466e52c7a382afa58da01b1dc75058f032ddf0839", "affectsGlobalScope": true}, {"version": "b541a838a13f9234aba650a825393ffc2292dc0fc87681a5d81ef0c96d281e7a", "affectsGlobalScope": true}, {"version": "e0275cd0e42990dc3a16f0b7c8bca3efe87f1c8ad404f80c6db1c7c0b828c59f", "affectsGlobalScope": true}, {"version": "811ec78f7fefcabbda4bfa93b3eb67d9ae166ef95f9bff989d964061cbf81a0c", "affectsGlobalScope": true}, {"version": "717937616a17072082152a2ef351cb51f98802fb4b2fdabd32399843875974ca", "affectsGlobalScope": true}, {"version": "d7e7d9b7b50e5f22c915b525acc5a49a7a6584cf8f62d0569e557c5cfc4b2ac2", "affectsGlobalScope": true}, {"version": "71c37f4c9543f31dfced6c7840e068c5a5aacb7b89111a4364b1d5276b852557", "affectsGlobalScope": true}, {"version": "576711e016cf4f1804676043e6a0a5414252560eb57de9faceee34d79798c850", "affectsGlobalScope": true}, {"version": "89c1b1281ba7b8a96efc676b11b264de7a8374c5ea1e6617f11880a13fc56dc6", "affectsGlobalScope": true}, {"version": "49ed889be54031e1044af0ad2c603d627b8bda8b50c1a68435fe85583901d072", "affectsGlobalScope": true}, {"version": "e93d098658ce4f0c8a0779e6cab91d0259efb88a318137f686ad76f8410ca270", "affectsGlobalScope": true}, {"version": "063600664504610fe3e99b717a1223f8b1900087fab0b4cad1496a114744f8df", "affectsGlobalScope": true}, {"version": "934019d7e3c81950f9a8426d093458b65d5aff2c7c1511233c0fd5b941e608ab", "affectsGlobalScope": true}, {"version": "bf14a426dbbf1022d11bd08d6b8e709a2e9d246f0c6c1032f3b2edb9a902adbe", "affectsGlobalScope": true}, {"version": "ec0104fee478075cb5171e5f4e3f23add8e02d845ae0165bfa3f1099241fa2aa", "affectsGlobalScope": true}, {"version": "2b72d528b2e2fe3c57889ca7baef5e13a56c957b946906d03767c642f386bbc3", "affectsGlobalScope": true}, {"version": "acae90d417bee324b1372813b5a00829d31c7eb670d299cd7f8f9a648ac05688", "affectsGlobalScope": true}, {"version": "368af93f74c9c932edd84c58883e736c9e3d53cec1fe24c0b0ff451f529ceab1", "affectsGlobalScope": true}, {"version": "33358442698bb565130f52ba79bfd3d4d484ac85fe33f3cb1759c54d18201393", "affectsGlobalScope": true}, {"version": "782dec38049b92d4e85c1585fbea5474a219c6984a35b004963b00beb1aab538", "affectsGlobalScope": true}, "282f98006ed7fa9bb2cd9bdbe2524595cfc4bcd58a0bb3232e4519f2138df811", "6222e987b58abfe92597e1273ad7233626285bc2d78409d4a7b113d81a83496b", "cbe726263ae9a7bf32352380f7e8ab66ee25b3457137e316929269c19e18a2be", "7f698624bbbb060ece7c0e51b7236520ebada74b747d7523c7df376453ed6fea", {"version": "4025a454b1ca489b179ee8c684bdd70ff8c1967e382076ade53e7e4653e1daec", "affectsGlobalScope": true}, {"version": "984c09345059b76fc4221c2c54e53511f4c27a0794dfd6e9f81dc60f0b564e05", "affectsGlobalScope": true}, {"version": "e0423e6f071df6befc98cdab3ab9c6daaa25c49d0d0fff33e194bc65b507fb43", "affectsGlobalScope": true}, "2faebd3f3d53964c95d291bc1545c20a5db8b9886d44bc1d7b0afb6ecc261841", "616a55b9694bdb765470c1f66dc9606779b250520a36b87f4de2e4c594cea9bc", {"version": "9aab7aec34d809b2868dd0f0743e47ff35c0795ec5072d825c4ba934206cc7d3", "affectsGlobalScope": true}, "8a8eb4ebffd85e589a1cc7c178e291626c359543403d58c9cd22b81fab5b1fb9", "ea2c3cf74f0c8d5ee5689c4c52cfbd27f14a76ef3fe4b342cb4f8b61119bd680", "269536033c45b92c13747175bf6d197b959559acb119247b3eb9a0eee250c8c5", "81212195a5a76330d166ecfd85eb7119e93d3b814177643fa8a10f4b40055fbf", "82e5a50e17833a10eb091923b7e429dc846d42f1c6161eb6beeb964288d98a15", "3ef2a48cf1b15a53a748b921b4e39c17f8de3a41839c359f5c2661eaace3894e", "cf76e2f59b26aea7c923015783083b4820d6c0d85cda2fec3a0f232aabfc56c3", "c0191592be8eb7906f99ac4b8798d80a585b94001ea1a5f50d6ce5b0d13a5c62", "52f5c39e78a90c1d8ed7db18f39d890b2e8464a3f44d4233617893f6648e317d", "a69e8bce30aea7ec98f3b6ddfbc378c92826fede01aafbaec703057c2503ea51", "97403268e8d1dcb6721073b5221109b4f7feff04a9e9251c8c75d7a6f80fff2c", "cb2dd9c1b310f31b23755300779641b717c4e71c296c2d82f7f26fd2c2636572", "54abfb58401d1fcc215266e2377b54a973921abc8bc972e1980e363449714a43", "95f2af3b172e7915e64bdaf82bfd2c02d5319fdf3c10a5cbf24bb6a058b5ee91", "e45518bedd748832f0f69511a15b97fb8bdab64723b3a814d5215606ef66f109", "96afdd0cb1aa6f70a7b684d3fad4629df0f30e709425ad88ce1bbdc47ea78239", "f99a1f041979b91a9e98f15b75656809fa9fe579bf3f7b0c28171621c5b24aeb", "5054509b9158455e9368a8c22c487af7e7e816bb5ca50abea9ead44fb8b71766", "3d3bc7f1634ad6496ec8edb6dfc6cd7f8a548d41d3410d8160fd1ac67c27d913", "7095c73d2bce0077fe1d7b10689af7b2339102e6f7e309feb663bac78152fe65", "f43c7434fe9ecdd68af31eaac19c979424f41e9f7360e5c69cbb750488fae8b1", "2558aa73e666d49fdb9e3979ca9e949a72db65b61dea8ac76bfc8535b6c4f0fa", "36264771e168d378c3bb8bcde67b1af70e5507d7d0ec89b9210f945c592529e6", "b41ab5799a2b21c577ebe4310ace6f9c9d7b04250d0d799a0f4480d0cb8e0c3f", "bfa50eee0cc328509039d3da660a018fdd8e51eb025c0a66895f5794e1394f50", "d8c0d9de3c95b1db25ab4143fb892b828a136dcba7c44a07a0ed9dbf9786b647", "d7f474d3110d2823e3da2fed5c58d93d69db3cae53b886d590cf68ea391830a4", "90d686c4bae65dda034701e44bbbc6c80e9c4ee0504cbf40795fe5de321341d1", "2e1ecc07283297f33239268e623b21a89a58f7eaffa1df3a8460f7482780181b", "4c50eb0c57f65b3107eab1c2cd58186c30e08affa9d5180797c32a9696c15a52", "0231e7d168e2af1f1006eca690261d26b014585c6857aa6a919e89e56a0ff4d6", "8f070824a46e05373994c424dbd1dc2e05ab16380243f015eaddde944462dc4b"], "root": [52, [67, 87]], "options": {"composite": true, "esModuleInterop": true, "jsx": 1, "jsxImportSource": "vue", "module": 99, "noImplicitThis": true, "skipLibCheck": true, "strict": true, "target": 99, "useDefineForClassFields": true}, "fileIdsList": [[51], [59], [53, 59, 60], [61], [53], [53, 54, 55, 57], [54, 55, 56, 57], [63, 64, 66], [50], [46], [47], [48, 49], [63, 65, 66], [57, 62], [57], [58, 63, 65, 66], [58, 72], [51, 58, 63, 65, 66, 75], [58, 66, 74], [58, 72, 79, 80], [58, 79, 80, 81, 82], [58, 72, 79], [58, 72, 78], [58, 63, 65, 66, 72, 73], [58, 73, 74, 85, 86], [58, 63, 65, 66, 70], [58, 63, 65, 66, 72], [58], [58, 67, 68, 69, 70, 71], [58, 69]], "referencedMap": [[52, 1], [60, 2], [61, 3], [62, 4], [54, 5], [55, 6], [57, 7], [65, 8], [51, 9], [47, 10], [48, 11], [50, 12], [64, 13], [66, 13], [63, 14], [58, 15], [77, 16], [78, 17], [76, 18], [75, 19], [81, 20], [80, 17], [83, 21], [82, 22], [84, 23], [79, 17], [74, 24], [87, 25], [85, 26], [73, 27], [86, 27], [69, 28], [67, 28], [72, 29], [70, 28], [71, 30], [68, 28]], "exportedModulesMap": [[52, 1], [60, 2], [61, 3], [62, 4], [54, 5], [55, 6], [57, 7], [65, 8], [51, 9], [47, 10], [48, 11], [50, 12], [64, 13], [66, 13], [63, 14], [58, 15], [77, 16], [78, 17], [76, 18], [75, 19], [81, 20], [80, 17], [83, 21], [82, 22], [84, 23], [79, 17], [74, 24], [87, 25], [85, 26], [73, 27], [86, 27], [69, 28], [67, 28], [72, 29], [70, 28], [71, 30], [68, 28]], "semanticDiagnosticsPerFile": [52, 60, 59, 61, 62, 54, 55, 57, 53, 56, 65, 44, 45, 8, 9, 11, 10, 2, 12, 13, 14, 15, 16, 17, 18, 19, 3, 4, 20, 24, 21, 22, 23, 25, 26, 27, 5, 28, 29, 30, 31, 6, 35, 32, 33, 34, 36, 7, 37, 42, 43, 38, 39, 40, 41, 1, 51, 47, 46, 48, 49, 50, 64, 66, 63, 58, 77, 78, [76, [{"file": "./src/main.ts", "start": 85, "length": 11, "messageText": "Cannot find module './App.vue' or its corresponding type declarations.", "category": 1, "code": 2307}]], [75, [{"file": "./src/router/index.ts", "start": 139, "length": 23, "messageText": "Cannot find module '../views/HomeView.vue' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/router/index.ts", "start": 533, "length": 29, "messageText": "Cannot find module '../views/AssessmentView.vue' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/router/index.ts", "start": 716, "length": 35, "messageText": "Cannot find module '../views/AssessmentDetailView.vue' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/router/index.ts", "start": 891, "length": 25, "messageText": "Cannot find module '../views/ReportView.vue' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/router/index.ts", "start": 1069, "length": 33, "messageText": "Cannot find module '../views/PaymentSuccessView.vue' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/router/index.ts", "start": 1253, "length": 32, "messageText": "Cannot find module '../views/PaymentCancelView.vue' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/router/index.ts", "start": 1418, "length": 24, "messageText": "Cannot find module '../views/AboutView.vue' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/router/index.ts", "start": 1579, "length": 26, "messageText": "Cannot find module '../views/PrivacyView.vue' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "./src/router/index.ts", "start": 1740, "length": 27, "messageText": "Cannot find module '../views/NotFoundView.vue' or its corresponding type declarations.", "category": 1, "code": 2307}]], [81, [{"file": "./src/services/assessmentservice.ts", "start": 5342, "length": 19, "code": 2416, "category": 1, "messageText": {"messageText": "Property 'generateBasicReport' in type 'ECRAssessmentService' is not assignable to the same property in base type 'AssessmentService'.", "category": 1, "code": 2416, "next": [{"messageText": "Type '(assessmentId: string) => Promise<BasicResult>' is not assignable to type '(assessmentId: string) => Promise<BasicReport>'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'Promise<BasicResult>' is not assignable to type 'Promise<BasicReport>'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'BasicResult' is missing the following properties from type 'BasicReport': scores, description, characteristics, suggestions", "category": 1, "code": 2739}]}]}]}}, {"file": "./src/services/assessmentservice.ts", "start": 6141, "length": 22, "code": 2416, "category": 1, "messageText": {"messageText": "Property 'generateDetailedReport' in type 'ECRAssessmentService' is not assignable to the same property in base type 'AssessmentService'.", "category": 1, "code": 2416, "next": [{"messageText": "Type '(assessmentId: string) => Promise<import(\"/Users/<USER>/Documents/cursor-project/ECR/src/types/assessment\").DetailedReport>' is not assignable to type '(assessmentId: string) => Promise<DetailedReport>'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'Promise<import(\"/Users/<USER>/Documents/cursor-project/ECR/src/types/assessment\").DetailedReport>' is not assignable to type 'Promise<DetailedReport>'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'DetailedReport' is missing the following properties from type 'DetailedReport': attachmentStyle, scores, description, characteristics, suggestions", "category": 1, "code": 2739}]}]}]}}]], 80, 83, 82, [84, [{"file": "./src/services/questionservice.ts", "start": 6742, "length": 15, "messageText": "Export declaration conflicts with exported declaration of 'QuestionService'.", "category": 1, "code": 2484}]], 79, 74, [87, [{"file": "./src/stores/index.ts", "start": 217, "length": 15, "messageText": "'\"./assessment\"' has no exported member named 'AssessmentStore'. Did you mean 'useAssessmentStore'?", "category": 1, "code": 2724, "relatedInformation": [{"file": "./src/stores/assessment.ts", "start": 217, "length": 18, "messageText": "'useAssessmentStore' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/stores/index.ts", "start": 269, "length": 12, "messageText": "'\"./payment\"' has no exported member named 'PaymentStore'. Did you mean 'usePaymentStore'?", "category": 1, "code": 2724, "relatedInformation": [{"file": "./src/stores/payment.ts", "start": 145, "length": 15, "messageText": "'usePaymentStore' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/stores/index.ts", "start": 315, "length": 7, "messageText": "Module '\"./ui\"' has no exported member 'UIStore'.", "category": 1, "code": 2305}, {"file": "./src/stores/index.ts", "start": 351, "length": 9, "messageText": "'\"./user\"' has no exported member named 'UserStore'. Did you mean 'useUserStore'?", "category": 1, "code": 2724, "relatedInformation": [{"file": "./src/stores/user.ts", "start": 158, "length": 12, "messageText": "'useUserStore' is declared here.", "category": 3, "code": 2728}]}, {"file": "./src/stores/index.ts", "start": 907, "length": 18, "messageText": "'useAssessmentStore' cannot be used as a value because it was imported using 'import type'.", "category": 1, "code": 1361, "relatedInformation": [{"file": "./src/stores/index.ts", "start": 413, "length": 18, "messageText": "'useAssessmentStore' was imported here.", "category": 3, "code": 1376}]}, {"file": "./src/stores/index.ts", "start": 946, "length": 10, "messageText": "'useUIStore' cannot be used as a value because it was imported using 'import type'.", "category": 1, "code": 1361, "relatedInformation": [{"file": "./src/stores/index.ts", "start": 517, "length": 10, "messageText": "'useUIStore' was imported here.", "category": 3, "code": 1376}]}, {"file": "./src/stores/index.ts", "start": 979, "length": 12, "messageText": "'useUserStore' cannot be used as a value because it was imported using 'import type'.", "category": 1, "code": 1361, "relatedInformation": [{"file": "./src/stores/index.ts", "start": 556, "length": 12, "messageText": "'useUserStore' was imported here.", "category": 3, "code": 1376}]}]], 85, [73, [{"file": "./src/stores/ui.ts", "start": 123, "length": 12, "messageText": "Import declaration conflicts with local declaration of 'Notification'.", "category": 1, "code": 2440}]], 86, 69, 67, [72, [{"file": "./src/types/index.ts", "start": 367, "length": 13, "messageText": "Duplicate identifier 'PaymentStatus'.", "category": 1, "code": 2300}, {"file": "./src/types/index.ts", "start": 671, "length": 13, "messageText": "Duplicate identifier 'PaymentStatus'.", "category": 1, "code": 2300}, {"file": "./src/types/index.ts", "start": 384, "length": 14, "messageText": "Duplicate identifier 'PaymentSession'.", "category": 1, "code": 2300}, {"file": "./src/types/index.ts", "start": 686, "length": 14, "messageText": "Duplicate identifier 'PaymentSession'.", "category": 1, "code": 2300}, {"file": "./src/types/index.ts", "start": 402, "length": 13, "messageText": "Duplicate identifier 'PaymentResult'.", "category": 1, "code": 2300}, {"file": "./src/types/index.ts", "start": 702, "length": 13, "messageText": "Duplicate identifier 'PaymentResult'.", "category": 1, "code": 2300}, {"file": "./src/types/index.ts", "start": 430, "length": 11, "messageText": "Duplicate identifier 'ApiResponse'.", "category": 1, "code": 2300}, {"file": "./src/types/index.ts", "start": 752, "length": 11, "messageText": "Duplicate identifier 'ApiResponse'.", "category": 1, "code": 2300}, {"file": "./src/types/index.ts", "start": 445, "length": 12, "messageText": "Duplicate identifier 'LoadingState'.", "category": 1, "code": 2300}, {"file": "./src/types/index.ts", "start": 767, "length": 12, "messageText": "Duplicate identifier 'LoadingState'.", "category": 1, "code": 2300}, {"file": "./src/types/index.ts", "start": 461, "length": 10, "messageText": "Duplicate identifier 'AsyncState'.", "category": 1, "code": 2300}, {"file": "./src/types/index.ts", "start": 783, "length": 10, "messageText": "Duplicate identifier 'AsyncState'.", "category": 1, "code": 2300}, {"file": "./src/types/index.ts", "start": 475, "length": 16, "messageText": "Duplicate identifier 'NotificationType'.", "category": 1, "code": 2300}, {"file": "./src/types/index.ts", "start": 797, "length": 16, "messageText": "Duplicate identifier 'NotificationType'.", "category": 1, "code": 2300}, {"file": "./src/types/index.ts", "start": 506, "length": 13, "messageText": "Duplicate identifier 'ButtonVariant'.", "category": 1, "code": 2300}, {"file": "./src/types/index.ts", "start": 884, "length": 13, "messageText": "Duplicate identifier 'ButtonVariant'.", "category": 1, "code": 2300}, {"file": "./src/types/index.ts", "start": 523, "length": 10, "messageText": "Duplicate identifier 'ButtonSize'.", "category": 1, "code": 2300}, {"file": "./src/types/index.ts", "start": 901, "length": 10, "messageText": "Duplicate identifier 'ButtonSize'.", "category": 1, "code": 2300}, {"file": "./src/types/index.ts", "start": 537, "length": 9, "messageText": "Duplicate identifier 'ModalSize'.", "category": 1, "code": 2300}, {"file": "./src/types/index.ts", "start": 915, "length": 9, "messageText": "Duplicate identifier 'ModalSize'.", "category": 1, "code": 2300}, {"file": "./src/types/index.ts", "start": 550, "length": 9, "messageText": "Duplicate identifier 'AlertType'.", "category": 1, "code": 2300}, {"file": "./src/types/index.ts", "start": 928, "length": 9, "messageText": "Duplicate identifier 'AlertType'.", "category": 1, "code": 2300}, {"file": "./src/types/index.ts", "start": 574, "length": 10, "messageText": "Duplicate identifier 'HttpMethod'.", "category": 1, "code": 2300}, {"file": "./src/types/index.ts", "start": 1003, "length": 10, "messageText": "Duplicate identifier 'HttpMethod'.", "category": 1, "code": 2300}, {"file": "./src/types/index.ts", "start": 588, "length": 13, "messageText": "Duplicate identifier 'RequestConfig'.", "category": 1, "code": 2300}, {"file": "./src/types/index.ts", "start": 1017, "length": 13, "messageText": "Duplicate identifier 'RequestConfig'.", "category": 1, "code": 2300}, {"file": "./src/types/index.ts", "start": 605, "length": 8, "messageText": "Duplicate identifier 'Response'.", "category": 1, "code": 2300}, {"file": "./src/types/index.ts", "start": 1034, "length": 8, "messageText": "Duplicate identifier 'Response'.", "category": 1, "code": 2300}, {"file": "./src/types/index.ts", "start": 617, "length": 16, "messageText": "Duplicate identifier 'ValidationResult'.", "category": 1, "code": 2300}, {"file": "./src/types/index.ts", "start": 1046, "length": 16, "messageText": "Duplicate identifier 'ValidationResult'.", "category": 1, "code": 2300}, {"file": "./src/types/index.ts", "start": 384, "length": 14, "messageText": "Module '\"./assessment\"' has no exported member 'PaymentSession'.", "category": 1, "code": 2305}, {"file": "./src/types/index.ts", "start": 402, "length": 13, "messageText": "Module '\"./assessment\"' has no exported member 'PaymentResult'.", "category": 1, "code": 2305}, {"file": "./src/types/index.ts", "start": 430, "length": 11, "messageText": "Module '\"./assessment\"' has no exported member 'ApiResponse'.", "category": 1, "code": 2305}, {"file": "./src/types/index.ts", "start": 445, "length": 12, "messageText": "Module '\"./assessment\"' has no exported member 'LoadingState'.", "category": 1, "code": 2305}, {"file": "./src/types/index.ts", "start": 461, "length": 10, "messageText": "Module '\"./assessment\"' has no exported member 'AsyncState'.", "category": 1, "code": 2305}, {"file": "./src/types/index.ts", "start": 475, "length": 16, "messageText": "Module '\"./assessment\"' has no exported member 'NotificationType'.", "category": 1, "code": 2305}, {"file": "./src/types/index.ts", "start": 506, "length": 13, "messageText": "Module '\"./assessment\"' has no exported member 'ButtonVariant'.", "category": 1, "code": 2305}, {"file": "./src/types/index.ts", "start": 523, "length": 10, "messageText": "Module '\"./assessment\"' has no exported member 'ButtonSize'.", "category": 1, "code": 2305}, {"file": "./src/types/index.ts", "start": 537, "length": 9, "messageText": "Module '\"./assessment\"' has no exported member 'ModalSize'.", "category": 1, "code": 2305}, {"file": "./src/types/index.ts", "start": 550, "length": 9, "messageText": "Module '\"./assessment\"' has no exported member 'AlertType'.", "category": 1, "code": 2305}, {"file": "./src/types/index.ts", "start": 574, "length": 10, "messageText": "Module '\"./assessment\"' has no exported member 'HttpMethod'.", "category": 1, "code": 2305}, {"file": "./src/types/index.ts", "start": 588, "length": 13, "messageText": "Module '\"./assessment\"' has no exported member 'RequestConfig'.", "category": 1, "code": 2305}, {"file": "./src/types/index.ts", "start": 605, "length": 8, "messageText": "Module '\"./assessment\"' has no exported member 'Response'.", "category": 1, "code": 2305}, {"file": "./src/types/index.ts", "start": 617, "length": 16, "messageText": "Module '\"./assessment\"' has no exported member 'ValidationResult'.", "category": 1, "code": 2305}]], 70, 71, [68, [{"file": "./src/types/ui.ts", "start": 2906, "length": 3, "messageText": "Cannot find namespace 'JSX'.", "category": 1, "code": 2503}, {"file": "./src/types/ui.ts", "start": 4370, "length": 3, "messageText": "Cannot find namespace 'JSX'.", "category": 1, "code": 2503}]]], "affectedFilesPendingEmit": [77, 78, 76, 75, 81, 80, 83, 82, 84, 79, 74, 87, 85, 73, 86, 69, 67, 72, 70, 71, 68], "emitSignatures": [67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87]}, "version": "5.3.3"}