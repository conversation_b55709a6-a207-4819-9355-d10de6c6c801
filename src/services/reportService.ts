import type {
  BasicReport,
  DetailedReportData,
  AttachmentTypeDescription,
  AttachmentScores,
  AttachmentPercentiles,
  ReportVisualization,
  ReportInsight,
  ReportRecommendation,
  ReportGenerationOptions,
  AttachmentStyle,
  BasicResult,
  DetailedReport
} from '@/types'

import { ATTACHMENT_DESCRIPTIONS } from '@/data/attachmentDescriptions'
import { reportCacheService } from './reportCacheService'
import { performanceTimer } from '@/utils/performance'

/**
 * 报告生成服务
 */
class ReportService {
  private readonly storageKey = 'ecr_reports'

  /**
   * 生成基础报告
   */
  generateBasicReport(
    assessmentId: string,
    basicResult: BasicResult,
    scores: AttachmentScores,
    reliability: number
  ): BasicReport {
    // 检查缓存
    const cached = reportCacheService.getCachedBasicReport(assessmentId, basicResult, scores)
    if (cached) {
      console.log('Using cached basic report for assessment:', assessmentId)
      return cached
    }

    // 开始性能计时
    performanceTimer.start(`basic_report_${assessmentId}`)
    const attachmentDescription = this.getAttachmentDescription(basicResult.attachmentStyle)
    const percentiles = this.calculatePercentiles(scores)

    const report: BasicReport = {
      id: `report_${assessmentId}_${Date.now()}`,
      assessmentId,
      type: 'basic',
      status: 'ready',
      createdAt: new Date(),
      updatedAt: new Date(),
      basicResult,
      attachmentDescription,
      scores,
      percentiles,
      reliability
    }

    // 结束性能计时
    const duration = performanceTimer.end(`basic_report_${assessmentId}`)
    if (duration !== null) {
      console.log(`Basic report generation took ${duration.toFixed(2)}ms`)
    }

    // 保存到缓存
    reportCacheService.cacheBasicReport(assessmentId, basicResult, scores, report)

    this.saveReport(report)
    return report
  }

  /**
   * 生成详细报告
   */
  generateDetailedReport(
    basicReport: BasicReport,
    options: ReportGenerationOptions = {
      includeVisualizations: true,
      includeInsights: true,
      includeRecommendations: true,
      includeResources: true,
      language: 'zh',
      format: 'web'
    }
  ): DetailedReportData {
    // 检查缓存
    const cached = reportCacheService.getCachedDetailedReport(
      basicReport.assessmentId,
      basicReport.basicResult,
      basicReport.scores
    )
    if (cached) {
      console.log('Using cached detailed report for assessment:', basicReport.assessmentId)
      return cached
    }

    // 开始性能计时
    performanceTimer.start(`detailed_report_${basicReport.assessmentId}`)
    const detailedContent = this.generateDetailedContent(basicReport.basicResult)
    const visualizations = options.includeVisualizations
      ? this.generateVisualizations(basicReport)
      : []
    const insights = options.includeInsights ? this.generateInsights(basicReport) : []
    const recommendations = options.includeRecommendations
      ? this.generateRecommendations(basicReport)
      : []

    const detailedReport: DetailedReportData = {
      ...basicReport,
      type: 'detailed',
      detailedContent,
      visualizations,
      insights,
      recommendations,
      accessInfo: {
        isUnlocked: false
      }
    }

    // 结束性能计时
    const duration = performanceTimer.end(`detailed_report_${basicReport.assessmentId}`)
    if (duration !== null) {
      console.log(`Detailed report generation took ${duration.toFixed(2)}ms`)
    }

    // 保存到缓存
    reportCacheService.cacheDetailedReport(
      basicReport.assessmentId,
      basicReport.basicResult,
      basicReport.scores,
      detailedReport
    )

    this.saveReport(detailedReport)
    return detailedReport
  }

  /**
   * 获取依恋类型描述
   */
  private getAttachmentDescription(style: AttachmentStyle): AttachmentTypeDescription {
    const description = ATTACHMENT_DESCRIPTIONS[style]
    if (!description) {
      throw new Error(`Unknown attachment style: ${style}`)
    }

    return {
      name: style,
      title: description.title,
      shortDescription: description.subtitle,
      longDescription: description.summary,
      characteristics: description.characteristics,
      strengths: description.strengths,
      challenges: description.challenges,
      relationshipPatterns: description.relationshipPatterns,
      icon: 'fas fa-heart',
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    }
  }

  /**
   * 计算百分位数
   */
  private calculatePercentiles(scores: AttachmentScores): AttachmentPercentiles {
    // 基于常模数据计算百分位数
    // 这里使用简化的计算，实际项目中应该使用真实的常模数据
    const anxiousPercentile = this.scoreToPercentile(scores.anxious, 3.56, 1.12)
    const avoidantPercentile = this.scoreToPercentile(scores.avoidant, 2.92, 1.25)
    const securePercentile = 100 - Math.max(anxiousPercentile, avoidantPercentile)

    return {
      anxious: Math.round(anxiousPercentile),
      avoidant: Math.round(avoidantPercentile),
      secure: Math.round(Math.max(0, securePercentile))
    }
  }

  /**
   * 分数转百分位数
   */
  private scoreToPercentile(score: number, mean: number, sd: number): number {
    const zScore = (score - mean) / sd
    // 简化的正态分布累积分布函数
    return this.normalCDF(zScore) * 100
  }

  /**
   * 正态分布累积分布函数（简化版）
   */
  private normalCDF(z: number): number {
    // 使用近似公式
    const t = 1 / (1 + 0.2316419 * Math.abs(z))
    const d = 0.3989423 * Math.exp((-z * z) / 2)
    let prob =
      d *
      t *
      (0.3193815 + t * (-0.3565638 + t * (1.781478 + t * (-1.821256 + t * 1.330274))))

    if (z > 0) prob = 1 - prob
    return prob
  }

  /**
   * 生成详细内容
   */
  private generateDetailedContent(basicResult: BasicResult): DetailedReport {
    const style = basicResult.attachmentStyle
    const description = ATTACHMENT_DESCRIPTIONS[style]

    return {
      personalityTraits: this.generatePersonalityTraits(style),
      relationshipPatterns: description.relationshipPatterns,
      growthSuggestions: this.generateGrowthSuggestions(style),
      strengthsAndChallenges: {
        strengths: description.strengths,
        challenges: description.challenges
      },
      compatibilityAnalysis: this.generateCompatibilityAnalysis(style)
    }
  }

  /**
   * 生成个性特征
   */
  private generatePersonalityTraits(style: AttachmentStyle): string[] {
    const traits: Record<AttachmentStyle, string[]> = {
      secure: [
        '情绪稳定，能够有效调节自己的情感',
        '在人际关系中表现出信任和开放',
        '具有良好的自我价值感和自信心',
        '能够平衡独立性和亲密关系的需求',
        '在面对冲突时采用建设性的解决方式'
      ],
      anxious: [
        '对关系中的变化和威胁高度敏感',
        '倾向于寻求更多的关注和确认',
        '情绪波动较大，容易感到焦虑和不安',
        '对伴侣的行为和态度过度解读',
        '在关系中可能表现出依赖性'
      ],
      avoidant: [
        '重视个人独立和自主性',
        '在情感表达上相对保守和克制',
        '倾向于通过理性分析处理问题',
        '对过度亲密感到不适或压力',
        '在关系中保持一定的情感距离'
      ],
      disorganized: [
        '在亲密关系中表现出矛盾的行为模式',
        '情绪调节能力相对较弱',
        '对关系的期望和恐惧并存',
        '可能在不同情境下表现出不同的依恋行为',
        '需要更多的耐心和理解来建立稳定关系'
      ]
    }

    return traits[style] || []
  }

  /**
   * 生成成长建议
   */
  private generateGrowthSuggestions(style: AttachmentStyle): string[] {
    const suggestions: Record<AttachmentStyle, string[]> = {
      secure: [
        '继续保持开放和诚实的沟通方式',
        '在关系中发挥积极的引导作用',
        '帮助伴侣建立更安全的依恋模式',
        '在面对挑战时保持乐观和韧性',
        '不断学习和成长，提升关系质量'
      ],
      anxious: [
        '学习情绪调节技巧，如深呼吸和正念练习',
        '培养自我安抚的能力，减少对外部确认的依赖',
        '练习理性思考，避免过度解读伴侣的行为',
        '建立健康的个人兴趣和社交圈子',
        '寻求专业心理咨询师的帮助和指导'
      ],
      avoidant: [
        '练习情感表达，学会分享内心的感受',
        '逐步增加与伴侣的情感连接和亲密度',
        '认识到依赖他人并不意味着失去独立性',
        '学习识别和表达自己的情感需求',
        '在安全的环境中练习脆弱性和开放性'
      ],
      disorganized: [
        '寻求专业心理治疗师的帮助',
        '学习情绪调节和压力管理技巧',
        '建立稳定和可预测的日常生活模式',
        '在关系中建立清晰的边界和期望',
        '耐心地工作于建立更一致的依恋模式'
      ]
    }

    return suggestions[style] || []
  }

  /**
   * 生成兼容性分析
   */
  private generateCompatibilityAnalysis(style: AttachmentStyle): string[] {
    const compatibility: Record<AttachmentStyle, string[]> = {
      secure: [
        '与各种依恋类型都能建立良好关系',
        '能够为焦虑型伴侣提供安全感和稳定性',
        '可以帮助回避型伴侣逐步开放和信任',
        '在关系中起到平衡和稳定的作用',
        '最适合与同样安全型的伴侣建立关系'
      ],
      anxious: [
        '与安全型伴侣最为匹配，能获得所需的安全感',
        '与回避型伴侣可能形成追逐-逃避的模式',
        '与同样焦虑型的伴侣可能产生情绪放大效应',
        '需要伴侣提供更多的确认和支持',
        '在稳定的关系中能够逐步改善依恋模式'
      ],
      avoidant: [
        '与安全型伴侣最为匹配，能在安全环境中逐步开放',
        '与焦虑型伴侣可能产生距离-追逐的循环',
        '与同样回避型的伴侣可能缺乏情感连接',
        '需要伴侣给予足够的空间和耐心',
        '在理解和接纳的环境中能够建立更深的连接'
      ],
      disorganized: [
        '与安全型伴侣最为匹配，能提供稳定的支持',
        '需要伴侣具备高度的理解和包容能力',
        '在专业指导下能够改善关系模式',
        '需要时间和耐心来建立稳定的关系',
        '通过治疗和成长可以发展出更健康的依恋模式'
      ]
    }

    return compatibility[style] || []
  }

  /**
   * 生成可视化图表
   */
  private generateVisualizations(report: BasicReport): ReportVisualization[] {
    const visualizations: ReportVisualization[] = []

    // 雷达图 - 依恋维度
    visualizations.push({
      id: 'attachment_radar',
      type: 'radar',
      title: '依恋维度分析',
      description: '您在不同依恋维度上的得分分布',
      data: [
        { label: '焦虑依恋', value: report.scores.anxious, color: '#ef4444' },
        { label: '回避依恋', value: report.scores.avoidant, color: '#3b82f6' },
        { label: '安全依恋', value: report.scores.secure, color: '#10b981' }
      ],
      config: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          r: {
            beginAtZero: true,
            max: 7,
            ticks: {
              stepSize: 1
            }
          }
        }
      }
    })

    // 柱状图 - 百分位数
    visualizations.push({
      id: 'percentile_bar',
      type: 'bar',
      title: '百分位数对比',
      description: '您的得分在人群中的相对位置',
      data: [
        { label: '焦虑依恋', value: report.percentiles.anxious, color: '#ef4444' },
        { label: '回避依恋', value: report.percentiles.avoidant, color: '#3b82f6' },
        { label: '安全依恋', value: report.percentiles.secure, color: '#10b981' }
      ],
      config: {
        responsive: true,
        scales: {
          y: {
            beginAtZero: true,
            max: 100,
            ticks: {
              callback: (value: number) => `${value}%`
            }
          }
        }
      }
    })

    return visualizations
  }

  /**
   * 生成洞察
   */
  private generateInsights(report: BasicReport): ReportInsight[] {
    const insights: ReportInsight[] = []
    const style = report.basicResult.attachmentStyle
    const scores = report.scores

    // 基于依恋类型生成洞察
    switch (style) {
      case 'secure':
        insights.push({
          id: 'secure_strength',
          category: 'personality',
          title: '情绪稳定的优势',
          content: '您具有良好的情绪调节能力，能够在关系中保持平衡和稳定。这是一个重要的优势，有助于建立健康的人际关系。',
          importance: 'high',
          icon: 'fas fa-heart',
          color: '#10b981'
        })
        break

      case 'anxious':
        insights.push({
          id: 'anxious_awareness',
          category: 'relationship',
          title: '关系敏感性',
          content: '您对关系中的变化非常敏感，这既是优势也是挑战。敏感性让您能够察觉到关系中的细微变化，但也可能导致过度担忧。',
          importance: 'high',
          icon: 'fas fa-exclamation-triangle',
          color: '#f59e0b'
        })
        break

      case 'avoidant':
        insights.push({
          id: 'avoidant_independence',
          category: 'personality',
          title: '独立性特质',
          content: '您重视个人独立和自主性，这在现代社会中是一个重要的优势。同时，学会在适当的时候寻求支持也很重要。',
          importance: 'medium',
          icon: 'fas fa-user',
          color: '#3b82f6'
        })
        break

      case 'disorganized':
        insights.push({
          id: 'disorganized_complexity',
          category: 'growth',
          title: '复杂的情感模式',
          content: '您的依恋模式较为复杂，这反映了丰富的内心世界。通过专业指导，您可以更好地理解和管理这些复杂的情感。',
          importance: 'high',
          icon: 'fas fa-puzzle-piece',
          color: '#ef4444'
        })
        break
    }

    // 基于分数生成额外洞察
    if (scores.anxious > 5.5) {
      insights.push({
        id: 'high_anxiety',
        category: 'growth',
        title: '焦虑管理的重要性',
        content: '您的焦虑依恋得分较高，建议学习一些焦虑管理技巧，如正念练习、深呼吸等，这将有助于改善您的关系体验。',
        importance: 'high',
        icon: 'fas fa-brain',
        color: '#ef4444'
      })
    }

    if (scores.avoidant > 5.5) {
      insights.push({
        id: 'high_avoidance',
        category: 'relationship',
        title: '亲密关系的挑战',
        content: '您的回避依恋得分较高，可能在建立深度亲密关系时遇到挑战。逐步练习情感表达和脆弱性分享会很有帮助。',
        importance: 'medium',
        icon: 'fas fa-shield-alt',
        color: '#3b82f6'
      })
    }

    return insights
  }

  /**
   * 生成建议
   */
  private generateRecommendations(report: BasicReport): ReportRecommendation[] {
    const recommendations: ReportRecommendation[] = []
    const style = report.basicResult.attachmentStyle
    const scores = report.scores

    // 立即行动建议
    recommendations.push({
      id: 'immediate_action',
      category: 'immediate',
      title: '立即可以开始的行动',
      description: '基于您的依恋类型，以下是一些可以立即开始实践的建议。',
      actionItems: this.getImmediateActions(style),
      priority: 'high'
    })

    // 短期目标
    recommendations.push({
      id: 'short_term_goals',
      category: 'short_term',
      title: '1-3个月的发展目标',
      description: '这些目标需要持续的努力和练习，建议制定具体的行动计划。',
      actionItems: this.getShortTermGoals(style),
      priority: 'medium'
    })

    // 长期发展
    recommendations.push({
      id: 'long_term_development',
      category: 'long_term',
      title: '长期个人发展方向',
      description: '这些是长期的发展方向，需要耐心和持续的努力。',
      actionItems: this.getLongTermGoals(style),
      priority: 'medium',
      resources: this.getRecommendedResources(style)
    })

    return recommendations
  }

  /**
   * 保存报告
   */
  private saveReport(report: BasicReport | DetailedReportData): void {
    try {
      const reports = this.getAllReports()
      reports.set(report.id, report)
      localStorage.setItem(this.storageKey, JSON.stringify(Array.from(reports.entries())))
    } catch (error) {
      console.error('Failed to save report:', error)
    }
  }

  /**
   * 获取所有报告
   */
  private getAllReports(): Map<string, BasicReport | DetailedReportData> {
    try {
      const stored = localStorage.getItem(this.storageKey)
      if (stored) {
        const entries = JSON.parse(stored)
        return new Map(entries)
      }
    } catch (error) {
      console.error('Failed to load reports:', error)
    }
    return new Map()
  }

  /**
   * 获取报告
   */
  getReport(reportId: string): BasicReport | DetailedReportData | null {
    const reports = this.getAllReports()
    return reports.get(reportId) || null
  }

  /**
   * 根据评估ID获取报告
   */
  getReportByAssessmentId(assessmentId: string): BasicReport | DetailedReportData | null {
    const reports = this.getAllReports()
    for (const report of reports.values()) {
      if (report.assessmentId === assessmentId) {
        return report
      }
    }
    return null
  }

  /**
   * 获取立即行动建议
   */
  private getImmediateActions(style: AttachmentStyle): string[] {
    const actions: Record<AttachmentStyle, string[]> = {
      secure: [
        '继续保持开放和诚实的沟通',
        '在关系中发挥积极的引导作用',
        '帮助身边的人建立更安全的依恋模式'
      ],
      anxious: [
        '练习深呼吸和正念冥想',
        '记录并识别触发焦虑的情境',
        '与信任的朋友分享您的感受'
      ],
      avoidant: [
        '每天花5分钟反思自己的情感状态',
        '主动向亲近的人表达一次感谢',
        '尝试在安全的环境中分享一个个人故事'
      ],
      disorganized: [
        '建立稳定的日常作息',
        '寻找一个可信赖的支持者',
        '开始记录情绪日记'
      ]
    }
    return actions[style] || []
  }

  /**
   * 获取短期目标
   */
  private getShortTermGoals(style: AttachmentStyle): string[] {
    const goals: Record<AttachmentStyle, string[]> = {
      secure: [
        '学习更多关于依恋理论的知识',
        '在工作或生活中担任导师角色',
        '参与志愿服务或社区活动'
      ],
      anxious: [
        '学习情绪调节技巧（如CBT技术）',
        '建立健康的个人兴趣爱好',
        '练习自我安抚和自我确认'
      ],
      avoidant: [
        '逐步增加与他人的情感连接',
        '练习表达需求和寻求帮助',
        '参加小组活动或团队项目'
      ],
      disorganized: [
        '寻求专业心理咨询师的帮助',
        '学习压力管理和情绪调节技巧',
        '建立稳定的社交支持网络'
      ]
    }
    return goals[style] || []
  }

  /**
   * 获取长期目标
   */
  private getLongTermGoals(style: AttachmentStyle): string[] {
    const goals: Record<AttachmentStyle, string[]> = {
      secure: [
        '成为他人的情感支持和榜样',
        '在专业领域发挥领导作用',
        '建立和维护长期稳定的关系'
      ],
      anxious: [
        '发展出更稳定的自我价值感',
        '建立安全稳定的长期关系',
        '学会在关系中保持独立性'
      ],
      avoidant: [
        '建立深度的亲密关系',
        '学会在关系中保持脆弱性',
        '平衡独立性和相互依赖'
      ],
      disorganized: [
        '发展出一致的依恋模式',
        '建立稳定的长期关系',
        '成为情绪调节的专家'
      ]
    }
    return goals[style] || []
  }

  /**
   * 获取推荐资源
   */
  private getRecommendedResources(style: AttachmentStyle): ReportResource[] {
    const baseResources: ReportResource[] = [
      {
        type: 'book',
        title: '依恋理论三部曲',
        author: '约翰·鲍尔比',
        description: '依恋理论的经典著作，深入理解依恋的形成和发展'
      },
      {
        type: 'book',
        title: '成人依恋访谈',
        author: '玛丽·梅因',
        description: '了解成人依恋模式的评估和理解方法'
      }
    ]

    const styleSpecificResources: Record<AttachmentStyle, ReportResource[]> = {
      secure: [
        {
          type: 'book',
          title: '情商',
          author: '丹尼尔·戈尔曼',
          description: '提升情绪智能，更好地理解和帮助他人'
        }
      ],
      anxious: [
        {
          type: 'book',
          title: '焦虑自救手册',
          author: '埃德蒙·伯恩',
          description: '学习管理焦虑的实用技巧和方法'
        },
        {
          type: 'app',
          title: 'Headspace',
          description: '正念冥想应用，帮助缓解焦虑和压力'
        }
      ],
      avoidant: [
        {
          type: 'book',
          title: '脆弱的力量',
          author: '布琳·布朗',
          description: '学习拥抱脆弱性，建立更深的人际连接'
        }
      ],
      disorganized: [
        {
          type: 'book',
          title: '身体从未忘记',
          author: '贝塞尔·范德科尔克',
          description: '理解创伤对依恋的影响及康复方法'
        }
      ]
    }

    return [...baseResources, ...styleSpecificResources[style]]
  }
}

// 导出单例实例
export const reportService = new ReportService()
export default reportService
