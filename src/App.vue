<template>
  <div id="app" class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
    <RouterView />

    <!-- 全局Toast容器 -->
    <ToastContainer />
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { RouterView } from 'vue-router'
import { useUIStore } from '@/stores/ui'
import ToastContainer from '@/components/common/ToastContainer.vue'

const uiStore = useUIStore()

onMounted(() => {
  // 初始化UI状态
  uiStore.init()
})
</script>

<style scoped>
#app {
  font-family:
    'PingFang SC',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    'Hiragino Sans GB',
    'Microsoft YaHei',
    Roboto,
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</style>
