<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8">
    <div class="max-w-6xl mx-auto px-4">
      <!-- 页面头部 -->
      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-gray-800 mb-4">测评报告</h1>
        <p class="text-gray-600">您的依恋类型分析结果</p>

        <!-- 报告状态指示器 -->
        <div class="flex items-center justify-center space-x-4 mt-4">
          <div class="flex items-center space-x-2">
            <div class="w-3 h-3 bg-green-500 rounded-full"></div>
            <span class="text-sm text-gray-600">基础报告已生成</span>
          </div>
          <div class="flex items-center space-x-2">
            <div :class="['w-3 h-3 rounded-full', isReportUnlocked ? 'bg-green-500' : 'bg-gray-300']"></div>
            <span class="text-sm text-gray-600">
              {{ isReportUnlocked ? '详细报告已解锁' : '详细报告未解锁' }}
            </span>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="flex items-center justify-center py-12">
        <div class="text-center">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p class="text-gray-600">正在生成报告...</p>
        </div>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-lg p-6 mb-8">
        <div class="text-center">
          <i class="fas fa-exclamation-triangle text-red-500 text-3xl mb-4"></i>
          <h3 class="text-lg font-semibold text-red-800 mb-2">报告生成失败</h3>
          <p class="text-red-600 mb-4">{{ error }}</p>
          <button
            @click="retryGenerateReport"
            class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
          >
            重试
          </button>
        </div>
      </div>

      <!-- 报告内容 -->
      <div v-else-if="basicReport" class="space-y-8">
        <!-- 依恋类型卡片 -->
        <AttachmentTypeCard
          :attachment-type="basicReport.basicResult.attachmentStyle"
          :description="basicReport.attachmentDescription"
          :confidence="calculateConfidence(basicReport.scores)"
          :is-unlocked="isReportUnlocked"
          @unlock-detailed="handleUnlockDetailed"
          @view-detailed="handleViewDetailed"
          @share="handleShare"
        />

        <!-- 得分展示和图表 -->
        <div class="grid lg:grid-cols-2 gap-8">
          <!-- 得分显示 -->
          <ScoreDisplay
            :scores="basicReport.scores"
            :percentiles="basicReport.percentiles"
            :reliability="basicReport.reliability"
            @view-chart="showChartModal = true"
            @compare="handleCompare"
            @export="handleExport"
          />

          <!-- 雷达图 -->
          <div class="bg-white rounded-2xl shadow-lg p-6">
            <RadarChart
              :data="radarChartData"
              title="依恋维度雷达图"
              description="您在不同依恋维度上的得分可视化"
              :show-actions="false"
            />
          </div>
        </div>

        <!-- 解锁详细报告卡片 -->
        <div v-if="!isReportUnlocked" class="bg-white rounded-2xl shadow-xl p-8 text-center">
          <div class="mb-6">
            <i class="fas fa-lock text-gray-400 text-4xl mb-4"></i>
            <h3 class="text-xl font-semibold text-gray-800 mb-2">解锁详细报告</h3>
            <p class="text-gray-600">获取更深入的分析、个性化建议和专业图表</p>
          </div>

          <div class="bg-gray-50 rounded-xl p-6 mb-6">
            <h4 class="font-semibold text-gray-800 mb-3">详细报告包含：</h4>
            <div class="grid md:grid-cols-2 gap-4 text-sm text-gray-600">
              <div class="flex items-center">
                <i class="fas fa-chart-pie text-blue-500 mr-2"></i>
                <span>专业可视化图表</span>
              </div>
              <div class="flex items-center">
                <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>
                <span>个性化改善建议</span>
              </div>
              <div class="flex items-center">
                <i class="fas fa-heart text-red-500 mr-2"></i>
                <span>关系发展指导</span>
              </div>
              <div class="flex items-center">
                <i class="fas fa-download text-green-500 mr-2"></i>
                <span>PDF报告下载</span>
              </div>
            </div>
          </div>

          <div class="flex items-center justify-center space-x-4 mb-6">
            <span class="text-2xl font-bold text-gray-800">¥19.9</span>
            <span class="text-sm text-gray-500 line-through">¥39.9</span>
            <span class="bg-red-100 text-red-600 px-2 py-1 rounded text-sm">限时5折</span>
          </div>

          <button
            @click="unlockDetailedReport"
            class="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-full text-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 shadow-lg"
          >
            解锁详细报告
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { usePaymentStore } from '@/stores/payment'
import { useAssessmentStore } from '@/stores/assessment'
import { useUIStore } from '@/stores/ui'
import reportService from '@/services/reportService'
import type { BasicReport, DetailedReportData, ChartDataPoint, AttachmentScores } from '@/types'

// 组件导入
import AttachmentTypeCard from '@/components/report/AttachmentTypeCard.vue'
import ScoreDisplay from '@/components/report/ScoreDisplay.vue'
import RadarChart from '@/components/charts/RadarChart.vue'

const route = useRoute()
const router = useRouter()
const paymentStore = usePaymentStore()
const assessmentStore = useAssessmentStore()
const uiStore = useUIStore()

// 响应式状态
const loading = ref(true)
const error = ref<string | null>(null)
const basicReport = ref<BasicReport | null>(null)
const detailedReport = ref<DetailedReportData | null>(null)
const showChartModal = ref(false)

// 计算属性
const assessmentId = computed(() => route.params.id as string)

const isReportUnlocked = computed(() => {
  return paymentStore.isReportUnlocked(assessmentId.value)
})

const radarChartData = computed((): ChartDataPoint[] => {
  if (!basicReport.value) return []

  return [
    {
      label: '焦虑依恋',
      value: basicReport.value.scores.anxious,
      color: '#ef4444'
    },
    {
      label: '回避依恋',
      value: basicReport.value.scores.avoidant,
      color: '#3b82f6'
    },
    {
      label: '安全依恋',
      value: basicReport.value.scores.secure,
      color: '#10b981'
    }
  ]
})

// 生命周期
onMounted(async () => {
  await loadReport()
})

// 方法
const loadReport = async () => {
  try {
    loading.value = true
    error.value = null

    // 健壮性判断，确保 assessments 已初始化
    if (!assessmentStore.assessments) {
      error.value = '测评数据未初始化，请刷新页面重试'
      return
    }
    // 直接通过 assessments Map 获取指定 id 的测评数据
    const assessment = assessmentStore.assessments.get(assessmentId.value)
    if (!assessment || !assessment.basicResult) {
      error.value = '未找到评估数据，请先完成测评'
      return
    }

    // 尝试加载现有报告
    let report = reportService.getReportByAssessmentId(assessmentId.value)

    if (!report) {
      // 生成基础报告
      report = reportService.generateBasicReport(
        assessmentId.value,
        assessment.basicResult,
        {
          anxious: assessment.basicResult.anxious,
          avoidant: assessment.basicResult.avoidant,
          secure: 7 - Math.max(assessment.basicResult.anxious, assessment.basicResult.avoidant)
        },
        0.85 // 默认可靠性
      )
    }

    basicReport.value = report

    // 如果已解锁，加载详细报告
    if (isReportUnlocked.value) {
      await loadDetailedReport()
    }

  } catch (err) {
    console.error('Failed to load report:', err)
    error.value = err instanceof Error ? err.message : '报告加载失败'
  } finally {
    loading.value = false
  }
}

const loadDetailedReport = async () => {
  if (!basicReport.value) return

  try {
    let detailed = reportService.getReportByAssessmentId(assessmentId.value) as DetailedReportData

    if (!detailed || detailed.type !== 'detailed') {
      // 生成详细报告
      detailed = reportService.generateDetailedReport(basicReport.value)
    }

    detailedReport.value = detailed
  } catch (err) {
    console.error('Failed to load detailed report:', err)
    uiStore.showError('详细报告加载失败')
  }
}

const calculateConfidence = (scores: AttachmentScores): number => {
  // 简化的置信度计算
  const maxScore = Math.max(scores.anxious, scores.avoidant, scores.secure)
  const minScore = Math.min(scores.anxious, scores.avoidant, scores.secure)
  const difference = maxScore - minScore

  // 差异越大，置信度越高
  return Math.min(95, Math.max(60, 60 + (difference * 10)))
}

const retryGenerateReport = () => {
  error.value = null
  loadReport()
}

const handleUnlockDetailed = async () => {
  try {
    await paymentStore.initiatePayment(assessmentId.value)
  } catch (err) {
    console.error('Payment initiation failed:', err)
    uiStore.showError('支付启动失败，请重试')
  }
}

const handleViewDetailed = () => {
  // 跳转到详细报告页面
  router.push(`/report/${assessmentId.value}/detailed`)
}

const handleShare = () => {
  if (navigator.share) {
    navigator.share({
      title: 'ECR依恋测评报告',
      text: '我刚完成了依恋测评，快来看看我的结果！',
      url: window.location.href
    })
  } else {
    // 复制链接到剪贴板
    navigator.clipboard.writeText(window.location.href)
    uiStore.showSuccess('链接已复制到剪贴板')
  }
}

const handleCompare = () => {
  uiStore.showInfo('对比分析功能即将推出')
}

const handleExport = () => {
  uiStore.showInfo('数据导出功能即将推出')
}

const unlockDetailedReport = () => {
  handleUnlockDetailed()
}
</script>