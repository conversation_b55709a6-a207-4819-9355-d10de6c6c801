<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
    <!-- 页面头部 -->
    <div class="bg-white shadow-sm border-b">
      <div class="max-w-6xl mx-auto px-4 py-6">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-800">详细报告</h1>
            <p class="text-gray-600 mt-1">深度分析您的依恋类型</p>
          </div>
          
          <div class="flex items-center space-x-4">
            <ReportExporter
              v-if="detailedReport"
              :report="detailedReport"
              @export="handleExport"
            />

            <ReportSharer
              v-if="detailedReport"
              :report="detailedReport"
              @share="handleShare"
            />

            <button
              @click="goBack"
              class="flex items-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              <i class="fas fa-arrow-left"></i>
              <span>返回</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <LoadingState
      v-if="loading"
      :visible="loading"
      type="spinner"
      size="lg"
      title="正在加载详细报告"
      message="请稍候，我们正在为您生成详细的分析报告..."
      :full-screen="false"
      :overlay="false"
    />

    <!-- 错误状态 -->
    <div v-else-if="error" class="max-w-6xl mx-auto px-4 py-8">
      <ErrorState
        type="server"
        title="报告加载失败"
        :description="error"
        :retryable="true"
        :retrying="loading"
        :show-back-button="true"
        :show-contact-button="true"
        @retry="retryLoad"
        @go-back="goBack"
        @contact="handleContact"
      />
    </div>

    <!-- 访问控制 -->
    <div v-else-if="!hasAccess" class="max-w-6xl mx-auto px-4 py-8">
      <AccessGuard
        access-type="payment"
        :resource-id="assessmentId"
        title="详细报告未解锁"
        message="请先购买详细报告以查看完整内容"
        :show-unlock-option="true"
        :show-retry-option="true"
        :show-back-option="true"
        @access-granted="handleAccessGranted"
        @access-denied="handleAccessDenied"
        @unlock-requested="handleUnlockRequested"
        @retry-requested="handleRetryRequested"
      />
    </div>

    <!-- 报告内容 -->
    <div v-else-if="detailedReport" class="max-w-6xl mx-auto px-4 py-8">
      <!-- 报告概览 -->
      <div class="bg-white rounded-2xl shadow-lg p-8 mb-8">
        <div class="text-center mb-6">
          <h2 class="text-2xl font-bold text-gray-800 mb-2">
            {{ detailedReport.attachmentDescription.title }}
          </h2>
          <p class="text-gray-600">{{ detailedReport.attachmentDescription.longDescription }}</p>
        </div>
        
        <!-- 得分展示 -->
        <div class="grid md:grid-cols-3 gap-6 mb-6">
          <div class="text-center">
            <div class="text-3xl font-bold text-red-500 mb-1">
              {{ detailedReport.scores.anxious.toFixed(1) }}
            </div>
            <div class="text-sm text-gray-600">焦虑依恋</div>
            <div class="text-xs text-gray-500">第{{ detailedReport.percentiles.anxious }}百分位</div>
          </div>
          <div class="text-center">
            <div class="text-3xl font-bold text-blue-500 mb-1">
              {{ detailedReport.scores.avoidant.toFixed(1) }}
            </div>
            <div class="text-sm text-gray-600">回避依恋</div>
            <div class="text-xs text-gray-500">第{{ detailedReport.percentiles.avoidant }}百分位</div>
          </div>
          <div class="text-center">
            <div class="text-3xl font-bold text-green-500 mb-1">
              {{ detailedReport.scores.secure.toFixed(1) }}
            </div>
            <div class="text-sm text-gray-600">安全依恋</div>
            <div class="text-xs text-gray-500">第{{ detailedReport.percentiles.secure }}百分位</div>
          </div>
        </div>
      </div>

      <!-- 可视化图表 -->
      <div class="grid lg:grid-cols-2 gap-8 mb-8">
        <!-- 四象限图 -->
        <div class="bg-white rounded-2xl shadow-lg p-6">
          <QuadrantChart
            :scores="detailedReport.scores"
            title="依恋类型四象限图"
            description="您在依恋模型中的位置"
            :show-actions="true"
          />
        </div>

        <!-- 柱状图 -->
        <div class="bg-white rounded-2xl shadow-lg p-6">
          <BarChart
            :data="barChartData"
            title="百分位数对比"
            description="您的得分在人群中的相对位置"
            :show-actions="true"
          />
        </div>
      </div>

      <!-- 更多可视化图表 -->
      <div class="grid lg:grid-cols-2 gap-8 mb-8">
        <!-- 环形进度图 -->
        <div class="bg-white rounded-2xl shadow-lg p-6">
          <div class="text-center mb-4">
            <h3 class="text-lg font-semibold text-gray-800">依恋维度得分</h3>
            <p class="text-sm text-gray-600 mt-1">各维度得分的可视化展示</p>
          </div>
          <div class="flex items-center justify-center space-x-8">
            <ProgressRing
              :value="Math.round((detailedReport.scores.anxious / 7) * 100)"
              :progress-color="'#ef4444'"
              label="焦虑依恋"
              :size="100"
            />
            <ProgressRing
              :value="Math.round((detailedReport.scores.avoidant / 7) * 100)"
              :progress-color="'#3b82f6'"
              label="回避依恋"
              :size="100"
            />
            <ProgressRing
              :value="Math.round((detailedReport.scores.secure / 7) * 100)"
              :progress-color="'#10b981'"
              label="安全依恋"
              :size="100"
            />
          </div>
        </div>

        <!-- 对比图表 -->
        <div class="bg-white rounded-2xl shadow-lg p-6">
          <ComparisonChart
            :data="comparisonData"
            title="依恋维度对比"
            description="各维度得分的详细对比"
            :show-actions="true"
            :interpretations="[
              '焦虑维度反映了您对关系丧失和拒绝的担忧程度',
              '回避维度反映了您对亲密关系的不适感和独立需求',
              '安全维度反映了您建立健康关系的能力'
            ]"
          />
        </div>
      </div>

      <!-- 个性特征分析 -->
      <div class="bg-white rounded-2xl shadow-lg p-8 mb-8">
        <h3 class="text-xl font-bold text-gray-800 mb-6">个性特征分析</h3>
        <div class="grid md:grid-cols-2 gap-6">
          <div>
            <h4 class="text-lg font-semibold text-gray-700 mb-3">核心特征</h4>
            <ul class="space-y-2">
              <li 
                v-for="trait in detailedReport.detailedContent.personalityTraits" 
                :key="trait"
                class="flex items-start"
              >
                <i class="fas fa-check-circle text-green-500 mr-2 mt-1"></i>
                <span class="text-gray-600">{{ trait }}</span>
              </li>
            </ul>
          </div>
          
          <div>
            <h4 class="text-lg font-semibold text-gray-700 mb-3">关系模式</h4>
            <ul class="space-y-2">
              <li 
                v-for="pattern in detailedReport.detailedContent.relationshipPatterns" 
                :key="pattern"
                class="flex items-start"
              >
                <i class="fas fa-heart text-pink-500 mr-2 mt-1"></i>
                <span class="text-gray-600">{{ pattern }}</span>
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- 优势与挑战 -->
      <div class="grid lg:grid-cols-2 gap-8 mb-8">
        <div class="bg-white rounded-2xl shadow-lg p-8">
          <h3 class="text-xl font-bold text-gray-800 mb-6">您的优势</h3>
          <ul class="space-y-3">
            <li 
              v-for="strength in detailedReport.detailedContent.strengthsAndChallenges.strengths" 
              :key="strength"
              class="flex items-start"
            >
              <i class="fas fa-star text-yellow-500 mr-3 mt-1"></i>
              <span class="text-gray-600">{{ strength }}</span>
            </li>
          </ul>
        </div>
        
        <div class="bg-white rounded-2xl shadow-lg p-8">
          <h3 class="text-xl font-bold text-gray-800 mb-6">成长空间</h3>
          <ul class="space-y-3">
            <li 
              v-for="challenge in detailedReport.detailedContent.strengthsAndChallenges.challenges" 
              :key="challenge"
              class="flex items-start"
            >
              <i class="fas fa-exclamation-circle text-orange-500 mr-3 mt-1"></i>
              <span class="text-gray-600">{{ challenge }}</span>
            </li>
          </ul>
        </div>
      </div>

      <!-- 成长建议 -->
      <div class="bg-white rounded-2xl shadow-lg p-8 mb-8">
        <h3 class="text-xl font-bold text-gray-800 mb-6">个性化成长建议</h3>
        <div class="grid md:grid-cols-2 gap-6">
          <div 
            v-for="(suggestion, index) in detailedReport.detailedContent.growthSuggestions" 
            :key="index"
            class="bg-blue-50 rounded-lg p-4"
          >
            <div class="flex items-start">
              <div class="flex-shrink-0 w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center mr-3">
                {{ index + 1 }}
              </div>
              <p class="text-gray-700">{{ suggestion }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 兼容性分析 -->
      <div class="bg-white rounded-2xl shadow-lg p-8">
        <h3 class="text-xl font-bold text-gray-800 mb-6">关系兼容性分析</h3>
        <div class="space-y-4">
          <div 
            v-for="analysis in detailedReport.detailedContent.compatibilityAnalysis" 
            :key="analysis"
            class="bg-purple-50 rounded-lg p-4"
          >
            <div class="flex items-start">
              <i class="fas fa-users text-purple-500 mr-3 mt-1"></i>
              <p class="text-gray-700">{{ analysis }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { usePaymentStore } from '@/stores/payment'
import { useAssessmentStore } from '@/stores/assessment'
import { useUIStore } from '@/stores/ui'
import reportService from '@/services/reportService'
import { useLoadingState, useErrorHandling, useClipboard, useNetworkStatus } from '@/composables/useUserExperience'
import type { DetailedReportData, ChartDataPoint } from '@/types'

// 组件导入
import QuadrantChart from '@/components/charts/QuadrantChart.vue'
import BarChart from '@/components/charts/BarChart.vue'
import ProgressRing from '@/components/charts/ProgressRing.vue'
import ComparisonChart from '@/components/charts/ComparisonChart.vue'
import ReportExporter from '@/components/report/ReportExporter.vue'
import ReportSharer from '@/components/report/ReportSharer.vue'
import AccessGuard from '@/components/common/AccessGuard.vue'
import LoadingState from '@/components/common/LoadingState.vue'
import ErrorState from '@/components/common/ErrorState.vue'

const route = useRoute()
const router = useRouter()
const paymentStore = usePaymentStore()
const assessmentStore = useAssessmentStore()
const uiStore = useUIStore()

// 用户体验组合式函数
const { loading, startLoading, stopLoading, updateProgress } = useLoadingState()
const { error, handleError, clearError, canRetry, incrementRetry } = useErrorHandling()
const { copyToClipboard } = useClipboard()
const { isOnline } = useNetworkStatus()

// 响应式状态
const detailedReport = ref<DetailedReportData | null>(null)

// 计算属性
const assessmentId = computed(() => route.params.id as string)

const hasAccess = computed(() => {
  return paymentStore.isReportUnlocked(assessmentId.value)
})

const barChartData = computed((): ChartDataPoint[] => {
  if (!detailedReport.value) return []

  return [
    {
      label: '焦虑依恋',
      value: detailedReport.value.percentiles.anxious,
      color: '#ef4444'
    },
    {
      label: '回避依恋',
      value: detailedReport.value.percentiles.avoidant,
      color: '#3b82f6'
    },
    {
      label: '安全依恋',
      value: detailedReport.value.percentiles.secure,
      color: '#10b981'
    }
  ]
})

const comparisonData = computed((): ChartDataPoint[] => {
  if (!detailedReport.value) return []

  return [
    {
      label: '焦虑依恋',
      value: detailedReport.value.scores.anxious,
      color: '#ef4444',
      description: '对关系丧失和拒绝的担忧程度'
    },
    {
      label: '回避依恋',
      value: detailedReport.value.scores.avoidant,
      color: '#3b82f6',
      description: '对亲密关系的不适感和独立需求'
    },
    {
      label: '安全依恋',
      value: detailedReport.value.scores.secure,
      color: '#10b981',
      description: '建立健康关系的能力和情绪稳定性'
    }
  ]
})

// 生命周期
onMounted(async () => {
  await loadDetailedReport()
})

// 方法
const loadDetailedReport = async () => {
  try {
    startLoading('正在加载详细报告...')
    clearError()

    // 检查网络状态
    if (!isOnline.value) {
      handleError('网络连接不可用，请检查网络设置', '加载详细报告')
      return
    }

    updateProgress(10, '检查访问权限...')

    // 检查访问权限
    if (!hasAccess.value) {
      stopLoading()
      return
    }

    updateProgress(30, '获取评估数据...')

    // 获取评估数据
    const assessment = assessmentStore.assessments?.get(assessmentId.value)
    if (!assessment || !assessment.basicResult) {
      handleError('未找到评估数据，请重新进行测评', '加载详细报告')
      return
    }

    updateProgress(50, '生成基础报告...')

    // 获取基础报告
    let basicReport = reportService.getReportByAssessmentId(assessmentId.value)
    if (!basicReport) {
      // 生成基础报告
      basicReport = reportService.generateBasicReport(
        assessmentId.value,
        assessment.basicResult,
        {
          anxious: assessment.basicResult.anxious,
          avoidant: assessment.basicResult.avoidant,
          secure: 7 - Math.max(assessment.basicResult.anxious, assessment.basicResult.avoidant)
        },
        0.85
      )
    }

    updateProgress(80, '生成详细分析...')

    // 生成详细报告
    const detailed = reportService.generateDetailedReport(basicReport)
    detailedReport.value = detailed

    updateProgress(100, '加载完成')

    // 显示成功消息
    uiStore.showSuccess('详细报告加载完成')

  } catch (err) {
    handleError(err, '加载详细报告')
  } finally {
    stopLoading()
  }
}

const retryLoad = () => {
  if (!canRetry.value) {
    uiStore.showError('重试次数已达上限，请稍后再试或联系客服')
    return
  }

  incrementRetry()
  clearError()
  loadDetailedReport()
}

const handleAccessGranted = () => {
  console.log('Access granted for detailed report')
  // 重新加载报告数据
  loadDetailedReport()
}

const handleAccessDenied = (reason: string) => {
  console.log('Access denied:', reason)
}

const handleUnlockRequested = async (resourceId: string) => {
  try {
    await paymentStore.initiatePayment(resourceId)
  } catch (err) {
    console.error('Payment initiation failed:', err)
    uiStore.showError('支付启动失败，请重试')
  }
}

const handleRetryRequested = () => {
  console.log('Retry access check requested')
  // 可以在这里添加重新检查逻辑
}

const handleContact = () => {
  uiStore.showInfo('客服功能即将推出，您可以通过邮件联系我们：<EMAIL>')
}

const handleExport = (options, personalInfo) => {
  console.log('导出报告:', options, personalInfo)
  uiStore.showInfo(`报告导出功能即将推出 (${options.format})`)
}

const handleShare = (platform, url) => {
  console.log('分享报告:', platform, url)
  uiStore.showSuccess(`已分享到${platform}`)
}

const goBack = () => {
  router.push(`/report/${assessmentId.value}`)
}
</script>
