<template>
  <div class="progress-ring-container">
    <div class="relative inline-block">
      <svg
        :width="size"
        :height="size"
        class="transform -rotate-90"
      >
        <!-- 背景圆环 -->
        <circle
          :cx="center"
          :cy="center"
          :r="radius"
          :stroke="backgroundColor"
          :stroke-width="strokeWidth"
          fill="transparent"
          class="transition-all duration-300"
        />
        
        <!-- 进度圆环 -->
        <circle
          :cx="center"
          :cy="center"
          :r="radius"
          :stroke="progressColor"
          :stroke-width="strokeWidth"
          :stroke-dasharray="circumference"
          :stroke-dashoffset="strokeDashoffset"
          :stroke-linecap="rounded ? 'round' : 'butt'"
          fill="transparent"
          class="transition-all duration-1000 ease-out"
        />
        
        <!-- 渐变定义 -->
        <defs v-if="gradient">
          <linearGradient :id="gradientId" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" :stop-color="gradient.from" />
            <stop offset="100%" :stop-color="gradient.to" />
          </linearGradient>
        </defs>
      </svg>
      
      <!-- 中心内容 -->
      <div 
        class="absolute inset-0 flex items-center justify-center"
        :style="{ width: size + 'px', height: size + 'px' }"
      >
        <div class="text-center">
          <!-- 百分比显示 -->
          <div 
            v-if="showPercentage"
            class="font-bold"
            :class="textSizeClass"
            :style="{ color: textColor }"
          >
            {{ animatedValue }}%
          </div>
          
          <!-- 自定义内容 -->
          <div v-if="label" class="text-xs text-gray-500 mt-1">
            {{ label }}
          </div>
          
          <!-- 插槽内容 -->
          <slot :value="value" :animatedValue="animatedValue"></slot>
        </div>
      </div>
    </div>
    
    <!-- 图例 -->
    <div v-if="showLegend" class="mt-4 text-center">
      <div class="text-sm font-medium text-gray-700">{{ title }}</div>
      <div v-if="description" class="text-xs text-gray-500 mt-1">{{ description }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'

interface Props {
  value: number // 0-100
  size?: number
  strokeWidth?: number
  backgroundColor?: string
  progressColor?: string
  textColor?: string
  showPercentage?: boolean
  label?: string
  title?: string
  description?: string
  showLegend?: boolean
  rounded?: boolean
  animated?: boolean
  gradient?: {
    from: string
    to: string
  }
}

const props = withDefaults(defineProps<Props>(), {
  size: 120,
  strokeWidth: 8,
  backgroundColor: '#e5e7eb',
  progressColor: '#3b82f6',
  textColor: '#1f2937',
  showPercentage: true,
  showLegend: false,
  rounded: true,
  animated: true
})

const animatedValue = ref(0)

// 计算属性
const center = computed(() => props.size / 2)
const radius = computed(() => (props.size - props.strokeWidth) / 2)
const circumference = computed(() => 2 * Math.PI * radius.value)

const strokeDashoffset = computed(() => {
  const progress = Math.min(Math.max(animatedValue.value, 0), 100)
  return circumference.value - (progress / 100) * circumference.value
})

const textSizeClass = computed(() => {
  if (props.size >= 150) return 'text-2xl'
  if (props.size >= 120) return 'text-xl'
  if (props.size >= 100) return 'text-lg'
  return 'text-base'
})

const gradientId = computed(() => `gradient-${Math.random().toString(36).substr(2, 9)}`)

const finalProgressColor = computed(() => {
  return props.gradient ? `url(#${gradientId.value})` : props.progressColor
})

// 生命周期
onMounted(() => {
  if (props.animated) {
    animateValue()
  } else {
    animatedValue.value = props.value
  }
})

// 监听值变化
watch(() => props.value, () => {
  if (props.animated) {
    animateValue()
  } else {
    animatedValue.value = props.value
  }
})

// 方法
const animateValue = () => {
  const startValue = animatedValue.value
  const endValue = props.value
  const duration = 1000 // 1秒动画
  const startTime = Date.now()

  const animate = () => {
    const currentTime = Date.now()
    const elapsed = currentTime - startTime
    const progress = Math.min(elapsed / duration, 1)

    // 使用缓动函数
    const easeOutQuart = 1 - Math.pow(1 - progress, 4)
    animatedValue.value = Math.round(startValue + (endValue - startValue) * easeOutQuart)

    if (progress < 1) {
      requestAnimationFrame(animate)
    }
  }

  requestAnimationFrame(animate)
}

// 获取进度颜色（基于值）
const getProgressColorByValue = (value: number): string => {
  if (value >= 80) return '#10b981' // 绿色
  if (value >= 60) return '#3b82f6' // 蓝色
  if (value >= 40) return '#f59e0b' // 黄色
  if (value >= 20) return '#f97316' // 橙色
  return '#ef4444' // 红色
}

// 导出方法供父组件使用
defineExpose({
  animateValue,
  getProgressColorByValue
})
</script>

<style scoped>
.progress-ring-container {
  @apply inline-block;
}

/* 进度环动画 */
.progress-ring-container svg circle:last-child {
  transition: stroke-dashoffset 1s ease-out;
}

/* 悬停效果 */
.progress-ring-container:hover svg circle:last-child {
  filter: brightness(1.1);
}

/* 响应式调整 */
@media (max-width: 640px) {
  .progress-ring-container {
    @apply scale-90;
  }
}
</style>
