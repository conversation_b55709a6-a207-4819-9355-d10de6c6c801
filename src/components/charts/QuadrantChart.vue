<template>
  <div class="quadrant-chart-container">
    <div v-if="title" class="chart-header mb-4">
      <h3 class="text-lg font-semibold text-gray-800">{{ title }}</h3>
      <p v-if="description" class="text-sm text-gray-600 mt-1">{{ description }}</p>
    </div>
    
    <div class="chart-wrapper" :style="{ height: height + 'px' }">
      <canvas ref="chartCanvas"></canvas>
    </div>
    
    <div v-if="showLegend" class="chart-legend mt-4 grid grid-cols-2 gap-4">
      <div 
        v-for="(quadrant, index) in quadrants" 
        :key="index"
        class="flex items-center p-2 rounded-lg"
        :class="quadrantBgClass(index)"
      >
        <div 
          class="w-4 h-4 rounded-full mr-2"
          :style="{ backgroundColor: quadrant.color }"
        ></div>
        <div>
          <div class="text-sm font-medium text-gray-800">{{ quadrant.name }}</div>
          <div class="text-xs text-gray-600">{{ quadrant.description }}</div>
        </div>
      </div>
    </div>
    
    <div v-if="showActions" class="chart-actions mt-4 flex justify-center space-x-2">
      <button
        @click="downloadChart"
        class="px-3 py-1 text-sm bg-blue-100 text-blue-600 rounded hover:bg-blue-200 transition-colors"
      >
        <i class="fas fa-download mr-1"></i>
        下载图表
      </button>
      <button
        @click="shareChart"
        class="px-3 py-1 text-sm bg-green-100 text-green-600 rounded hover:bg-green-200 transition-colors"
      >
        <i class="fas fa-share mr-1"></i>
        分享图表
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import {
  Chart as ChartJS,
  LinearScale,
  PointElement,
  LineElement,
  Tooltip,
  Legend,
  type ChartConfiguration
} from 'chart.js'
import type { AttachmentScores } from '@/types'

// 注册Chart.js组件
ChartJS.register(LinearScale, PointElement, LineElement, Tooltip, Legend)

interface QuadrantInfo {
  name: string
  description: string
  color: string
}

interface Props {
  scores: AttachmentScores
  title?: string
  description?: string
  height?: number
  showLegend?: boolean
  showActions?: boolean
  animated?: boolean
  xAxisLabel?: string
  yAxisLabel?: string
  threshold?: number
}

const props = withDefaults(defineProps<Props>(), {
  height: 300,
  showLegend: true,
  showActions: true,
  animated: true,
  xAxisLabel: '回避维度',
  yAxisLabel: '焦虑维度',
  threshold: 4
})

const emit = defineEmits<{
  download: [canvas: HTMLCanvasElement]
  share: [dataUrl: string]
}>()

const chartCanvas = ref<HTMLCanvasElement>()
let chartInstance: ChartJS | null = null

// 四象限信息
const quadrants = [
  {
    name: '安全型',
    description: '低焦虑 + 低回避',
    color: '#10b981' // 绿色
  },
  {
    name: '焦虑型',
    description: '高焦虑 + 低回避',
    color: '#f59e0b' // 黄色
  },
  {
    name: '回避型',
    description: '低焦虑 + 高回避',
    color: '#3b82f6' // 蓝色
  },
  {
    name: '混乱型',
    description: '高焦虑 + 高回避',
    color: '#ef4444' // 红色
  }
]

onMounted(async () => {
  await nextTick()
  initChart()
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.destroy()
  }
})

watch(() => props.scores, () => {
  updateChart()
}, { deep: true })

const initChart = () => {
  if (!chartCanvas.value) return

  const ctx = chartCanvas.value.getContext('2d')
  if (!ctx) return

  const config: ChartConfiguration = {
    type: 'scatter',
    data: {
      datasets: [
        // 用户数据点
        {
          label: '您的依恋类型',
          data: [{
            x: props.scores.avoidant,
            y: props.scores.anxious
          }],
          backgroundColor: getUserQuadrantColor(),
          borderColor: getUserQuadrantColor(),
          borderWidth: 2,
          pointRadius: 8,
          pointHoverRadius: 10
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      animation: props.animated ? {
        duration: 1000,
        easing: 'easeInOutQuart'
      } : false,
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          titleColor: 'white',
          bodyColor: 'white',
          borderColor: 'rgba(255, 255, 255, 0.1)',
          borderWidth: 1,
          cornerRadius: 8,
          displayColors: false,
          callbacks: {
            label: (context) => {
              const point = context.raw as { x: number; y: number }
              return [
                `${props.xAxisLabel}: ${point.x.toFixed(1)}`,
                `${props.yAxisLabel}: ${point.y.toFixed(1)}`
              ]
            }
          }
        }
      },
      scales: {
        x: {
          min: 1,
          max: 7,
          title: {
            display: true,
            text: props.xAxisLabel,
            color: '#6b7280'
          },
          grid: {
            color: (context) => {
              return context.tick.value === props.threshold ? 'rgba(0, 0, 0, 0.3)' : 'rgba(0, 0, 0, 0.1)'
            },
            lineWidth: (context) => {
              return context.tick.value === props.threshold ? 2 : 1
            }
          },
          ticks: {
            color: '#6b7280'
          }
        },
        y: {
          min: 1,
          max: 7,
          title: {
            display: true,
            text: props.yAxisLabel,
            color: '#6b7280'
          },
          grid: {
            color: (context) => {
              return context.tick.value === props.threshold ? 'rgba(0, 0, 0, 0.3)' : 'rgba(0, 0, 0, 0.1)'
            },
            lineWidth: (context) => {
              return context.tick.value === props.threshold ? 2 : 1
            }
          },
          ticks: {
            color: '#6b7280'
          }
        }
      }
    }
  }

  chartInstance = new ChartJS(ctx, config)
}

const updateChart = () => {
  if (!chartInstance) return

  const dataset = chartInstance.data.datasets[0]
  if (dataset) {
    dataset.data = [{
      x: props.scores.avoidant,
      y: props.scores.anxious
    }]
    dataset.backgroundColor = getUserQuadrantColor()
    dataset.borderColor = getUserQuadrantColor()
  }
  
  chartInstance.update('active')
}

const getUserQuadrantColor = (): string => {
  const { anxious, avoidant } = props.scores
  const threshold = props.threshold

  if (anxious < threshold && avoidant < threshold) {
    return quadrants[0].color // 安全型
  } else if (anxious >= threshold && avoidant < threshold) {
    return quadrants[1].color // 焦虑型
  } else if (anxious < threshold && avoidant >= threshold) {
    return quadrants[2].color // 回避型
  } else {
    return quadrants[3].color // 混乱型
  }
}

const getUserQuadrantIndex = (): number => {
  const { anxious, avoidant } = props.scores
  const threshold = props.threshold

  if (anxious < threshold && avoidant < threshold) {
    return 0 // 安全型
  } else if (anxious >= threshold && avoidant < threshold) {
    return 1 // 焦虑型
  } else if (anxious < threshold && avoidant >= threshold) {
    return 2 // 回避型
  } else {
    return 3 // 混乱型
  }
}

const quadrantBgClass = (index: number): string => {
  const userQuadrant = getUserQuadrantIndex()
  if (index === userQuadrant) {
    return 'bg-gray-100 border border-gray-200'
  }
  return ''
}

const downloadChart = () => {
  if (!chartCanvas.value) return
  emit('download', chartCanvas.value)
}

const shareChart = () => {
  if (!chartCanvas.value) return
  const dataUrl = chartCanvas.value.toDataURL('image/png')
  emit('share', dataUrl)
}
</script>

<style scoped>
.quadrant-chart-container {
  @apply w-full;
}

.chart-wrapper {
  @apply relative w-full;
}

.chart-header {
  @apply text-center;
}

.chart-legend {
  @apply text-center;
}

.chart-actions {
  @apply flex flex-wrap gap-2;
}

@media (max-width: 640px) {
  .chart-legend {
    @apply grid-cols-1;
  }
  
  .chart-actions {
    @apply flex-col items-center;
  }
}
</style>
