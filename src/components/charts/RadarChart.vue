<template>
  <div ref="chartContainer" class="relative">
    <div v-if="title" class="mb-4">
      <h3 class="text-lg font-semibold text-gray-800">{{ title }}</h3>
      <p v-if="description" class="text-sm text-gray-600">{{ description }}</p>
    </div>

    <div class="relative" :style="{ height: chartHeight }">
      <canvas
        :id="chartId"
        ref="canvasRef"
        class="w-full h-full"
      ></canvas>

      <!-- 性能信息（开发环境） -->
      <div v-if="$dev && getRenderStats().renderCount > 0" class="absolute top-2 right-2 text-xs bg-black bg-opacity-50 text-white p-1 rounded">
        渲染: {{ getRenderStats().renderCount }}次 | 平均: {{ getRenderStats().averageRenderTime.toFixed(1) }}ms
      </div>
    </div>

    <div v-if="showActions" class="mt-4 flex justify-center space-x-4">
      <button
        @click="downloadChart"
        class="text-sm text-blue-600 hover:text-blue-700 transition-colors"
      >
        <i class="fas fa-download mr-1"></i>
        下载图表
      </button>
      <button
        @click="refreshChart"
        class="text-sm text-gray-600 hover:text-gray-700 transition-colors"
      >
        <i class="fas fa-refresh mr-1"></i>
        刷新
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick, computed } from 'vue'
import {
  Chart as ChartJS,
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  Tooltip,
  Legend,
  type ChartConfiguration
} from 'chart.js'
import type { ChartDataPoint } from '@/types'
import {
  useChartPerformance,
  useChartDataOptimization,
  useChartResponsive,
  useChartAnimation,
  useChartMemoryOptimization
} from '@/composables/useChartPerformance'

// 注册Chart.js组件
ChartJS.register(
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  Tooltip,
  Legend
)

interface Props {
  data: ChartDataPoint[]
  title?: string
  description?: string
  showActions?: boolean
  height?: string
  options?: any
}

const props = withDefaults(defineProps<Props>(), {
  showActions: true,
  height: '400px'
})

const canvasRef = ref<HTMLCanvasElement>()
const chartContainer = ref<HTMLElement>()
const chartInstance = ref<ChartJS | null>(null)
const chartId = ref(`radar-chart-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`)

const chartHeight = ref(props.height)

// 性能优化hooks
const { startRender, endRender, getRenderStats } = useChartPerformance()
const { sampleData, getCachedData } = useChartDataOptimization()
const { containerSize, isVisible } = useChartResponsive(chartContainer)
const { getOptimalAnimationConfig } = useChartAnimation()
const { registerChart, unregisterChart } = useChartMemoryOptimization()

// 优化后的数据
const optimizedData = computed(() => {
  const cacheKey = `radar_data_${JSON.stringify(props.data)}`
  return getCachedData(cacheKey, () => props.data)
})

// 创建图表配置
const createChartConfig = (): ChartConfiguration<'radar'> => {
  const animationConfig = getOptimalAnimationConfig()

  return {
    type: 'radar',
    data: {
      labels: optimizedData.value.map(item => item.label),
      datasets: [{
        label: '得分',
        data: optimizedData.value.map(item => item.value),
        backgroundColor: 'rgba(59, 130, 246, 0.2)',
        borderColor: 'rgba(59, 130, 246, 1)',
        borderWidth: 2,
        pointBackgroundColor: props.data.map(item => item.color || '#3b82f6'),
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: props.data.map(item => item.color || '#3b82f6'),
        pointRadius: 5,
        pointHoverRadius: 7
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          titleColor: '#fff',
          bodyColor: '#fff',
          borderColor: 'rgba(59, 130, 246, 1)',
          borderWidth: 1,
          callbacks: {
            label(context) {
              return `${context.label}: ${context.parsed.r.toFixed(1)}分`
            }
          }
        }
      },
      scales: {
        r: {
          beginAtZero: true,
          max: 7,
          min: 0,
          ticks: {
            stepSize: 1,
            display: true,
            color: '#6b7280',
            font: {
              size: 12
            }
          },
          grid: {
            color: 'rgba(156, 163, 175, 0.3)'
          },
          angleLines: {
            color: 'rgba(156, 163, 175, 0.3)'
          },
          pointLabels: {
            color: '#374151',
            font: {
              size: 14,
              weight: '500'
            }
          }
        }
      },
      animation: animationConfig,
      ...props.options
    }
  }
}

// 初始化图表
const initChart = async () => {
  if (!canvasRef.value || optimizedData.value.length === 0 || !isVisible.value) return

  // 销毁现有图表实例
  destroyChart()

  await nextTick()

  try {
    startRender(chartId.value)
    const config = createChartConfig()
    chartInstance.value = new ChartJS(canvasRef.value, config)
    registerChart(chartInstance.value)
    endRender(chartId.value)
  } catch (error) {
    console.error('Failed to create radar chart:', error)
  }
}

// 销毁图表
const destroyChart = () => {
  if (chartInstance.value) {
    unregisterChart(chartInstance.value)
    chartInstance.value.destroy()
    chartInstance.value = null
  }
}

// 更新图表数据
const updateChart = () => {
  if (!chartInstance.value || optimizedData.value.length === 0 || !isVisible.value) return

  startRender(chartId.value)
  const chart = chartInstance.value
  chart.data.labels = optimizedData.value.map(item => item.label)
  chart.data.datasets[0].data = optimizedData.value.map(item => item.value)
  chart.data.datasets[0].pointBackgroundColor = props.data.map(item => item.color || '#3b82f6')
  chart.data.datasets[0].pointHoverBorderColor = props.data.map(item => item.color || '#3b82f6')
  
  chart.update('active')
}

// 下载图表
const downloadChart = () => {
  if (!chartInstance.value) return
  
  const link = document.createElement('a')
  link.download = `radar-chart-${Date.now()}.png`
  link.href = chartInstance.value.toBase64Image()
  link.click()
}

// 刷新图表
const refreshChart = () => {
  initChart()
}

// 监听数据变化
watch(() => props.data, () => {
  if (chartInstance.value) {
    updateChart()
  } else {
    initChart()
  }
}, { deep: true })

// 监听高度变化
watch(() => props.height, (newHeight) => {
  chartHeight.value = newHeight
  if (chartInstance.value) {
    nextTick(() => {
      chartInstance.value?.resize()
    })
  }
})

// 生命周期
onMounted(() => {
  initChart()
})

onUnmounted(() => {
  destroyChart()
})
</script>

<style scoped>
canvas {
  max-height: 100%;
  max-width: 100%;
}
</style>