<template>
  <div class="comparison-chart-container">
    <div v-if="title" class="chart-header mb-6">
      <h3 class="text-lg font-semibold text-gray-800">{{ title }}</h3>
      <p v-if="description" class="text-sm text-gray-600 mt-1">{{ description }}</p>
    </div>
    
    <div class="comparison-content">
      <!-- 对比项目 -->
      <div class="space-y-6">
        <div 
          v-for="(item, index) in comparisonData" 
          :key="index"
          class="comparison-item"
        >
          <div class="flex items-center justify-between mb-2">
            <div class="flex items-center space-x-2">
              <div 
                class="w-4 h-4 rounded-full"
                :style="{ backgroundColor: item.color }"
              ></div>
              <span class="text-sm font-medium text-gray-700">{{ item.label }}</span>
            </div>
            <span class="text-sm font-bold text-gray-800">{{ item.value.toFixed(1) }}</span>
          </div>
          
          <!-- 进度条 -->
          <div class="relative">
            <div class="w-full bg-gray-200 rounded-full h-3">
              <div 
                class="h-3 rounded-full transition-all duration-1000 ease-out"
                :style="{ 
                  width: getPercentage(item.value) + '%',
                  backgroundColor: item.color 
                }"
              ></div>
            </div>
            
            <!-- 平均线标记 -->
            <div 
              v-if="showAverage"
              class="absolute top-0 h-3 w-0.5 bg-gray-600"
              :style="{ left: getPercentage(averageValue) + '%' }"
              :title="`平均值: ${averageValue.toFixed(1)}`"
            >
              <div class="absolute -top-6 -left-4 text-xs text-gray-600 whitespace-nowrap">
                平均
              </div>
            </div>
          </div>
          
          <!-- 描述文本 -->
          <div v-if="item.description" class="mt-2 text-xs text-gray-500">
            {{ item.description }}
          </div>
        </div>
      </div>
      
      <!-- 统计信息 -->
      <div v-if="showStats" class="mt-8 grid grid-cols-2 md:grid-cols-4 gap-4">
        <div class="text-center p-3 bg-gray-50 rounded-lg">
          <div class="text-lg font-bold text-gray-800">{{ maxValue.toFixed(1) }}</div>
          <div class="text-xs text-gray-600">最高分</div>
        </div>
        <div class="text-center p-3 bg-gray-50 rounded-lg">
          <div class="text-lg font-bold text-gray-800">{{ minValue.toFixed(1) }}</div>
          <div class="text-xs text-gray-600">最低分</div>
        </div>
        <div class="text-center p-3 bg-gray-50 rounded-lg">
          <div class="text-lg font-bold text-gray-800">{{ averageValue.toFixed(1) }}</div>
          <div class="text-xs text-gray-600">平均分</div>
        </div>
        <div class="text-center p-3 bg-gray-50 rounded-lg">
          <div class="text-lg font-bold text-gray-800">{{ rangeValue.toFixed(1) }}</div>
          <div class="text-xs text-gray-600">分数差</div>
        </div>
      </div>
      
      <!-- 解释说明 -->
      <div v-if="interpretations && interpretations.length > 0" class="mt-6">
        <h4 class="text-sm font-semibold text-gray-700 mb-3">结果解释</h4>
        <div class="space-y-2">
          <div 
            v-for="(interpretation, index) in interpretations" 
            :key="index"
            class="bg-blue-50 rounded-lg p-3"
          >
            <div class="flex items-start">
              <i class="fas fa-info-circle text-blue-500 mr-2 mt-0.5"></i>
              <p class="text-sm text-blue-700">{{ interpretation }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 操作按钮 -->
    <div v-if="showActions" class="mt-6 flex justify-center space-x-2">
      <button
        @click="downloadChart"
        class="px-3 py-1 text-sm bg-blue-100 text-blue-600 rounded hover:bg-blue-200 transition-colors"
      >
        <i class="fas fa-download mr-1"></i>
        下载图表
      </button>
      <button
        @click="shareChart"
        class="px-3 py-1 text-sm bg-green-100 text-green-600 rounded hover:bg-green-200 transition-colors"
      >
        <i class="fas fa-share mr-1"></i>
        分享图表
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { ChartDataPoint } from '@/types'

interface ComparisonItem extends ChartDataPoint {
  description?: string
}

interface Props {
  data: ComparisonItem[]
  title?: string
  description?: string
  maxScale?: number
  showAverage?: boolean
  showStats?: boolean
  showActions?: boolean
  interpretations?: string[]
}

const props = withDefaults(defineProps<Props>(), {
  maxScale: 7,
  showAverage: true,
  showStats: true,
  showActions: true
})

const emit = defineEmits<{
  download: [data: ComparisonItem[]]
  share: [data: ComparisonItem[]]
}>()

// 计算属性
const comparisonData = computed(() => props.data)

const maxValue = computed(() => {
  return Math.max(...comparisonData.value.map(item => item.value))
})

const minValue = computed(() => {
  return Math.min(...comparisonData.value.map(item => item.value))
})

const averageValue = computed(() => {
  const sum = comparisonData.value.reduce((acc, item) => acc + item.value, 0)
  return sum / comparisonData.value.length
})

const rangeValue = computed(() => {
  return maxValue.value - minValue.value
})

// 方法
const getPercentage = (value: number): number => {
  return (value / props.maxScale) * 100
}

const downloadChart = () => {
  emit('download', comparisonData.value)
}

const shareChart = () => {
  emit('share', comparisonData.value)
}

// 获取颜色建议
const getColorByValue = (value: number): string => {
  const percentage = (value / props.maxScale) * 100
  
  if (percentage >= 80) return '#ef4444' // 红色 - 高分
  if (percentage >= 60) return '#f59e0b' // 黄色 - 中高分
  if (percentage >= 40) return '#3b82f6' // 蓝色 - 中等分
  if (percentage >= 20) return '#10b981' // 绿色 - 低分
  return '#6b7280' // 灰色 - 很低分
}

// 获取解释文本
const getInterpretationByValue = (value: number, label: string): string => {
  const percentage = (value / props.maxScale) * 100
  
  if (percentage >= 80) {
    return `您的${label}得分很高，这可能在某些情况下带来挑战。`
  } else if (percentage >= 60) {
    return `您的${label}得分较高，需要适当关注和管理。`
  } else if (percentage >= 40) {
    return `您的${label}得分处于中等水平，比较平衡。`
  } else if (percentage >= 20) {
    return `您的${label}得分较低，这通常是积极的表现。`
  } else {
    return `您的${label}得分很低，表现非常良好。`
  }
}

// 导出方法供父组件使用
defineExpose({
  getColorByValue,
  getInterpretationByValue,
  maxValue,
  minValue,
  averageValue,
  rangeValue
})
</script>

<style scoped>
.comparison-chart-container {
  @apply w-full;
}

.comparison-item {
  @apply relative;
}

/* 进度条动画 */
.comparison-item .h-3 {
  animation: progressFill 1s ease-out;
}

@keyframes progressFill {
  from {
    width: 0%;
  }
}

/* 悬停效果 */
.comparison-item:hover .h-3 {
  @apply shadow-lg;
  filter: brightness(1.1);
}

/* 平均线样式 */
.comparison-item .absolute.w-0.5 {
  @apply opacity-75;
}

.comparison-item:hover .absolute.w-0.5 {
  @apply opacity-100;
}

/* 响应式调整 */
@media (max-width: 640px) {
  .chart-header {
    @apply text-center;
  }
  
  .comparison-content {
    @apply px-2;
  }
}
</style>
