<template>
  <div class="bar-chart-container">
    <div v-if="title" class="chart-header mb-4">
      <h3 class="text-lg font-semibold text-gray-800">{{ title }}</h3>
      <p v-if="description" class="text-sm text-gray-600 mt-1">{{ description }}</p>
    </div>
    
    <div class="chart-wrapper" :style="{ height: height + 'px' }">
      <canvas ref="chartCanvas"></canvas>
    </div>
    
    <div v-if="showActions" class="chart-actions mt-4 flex justify-center space-x-2">
      <button
        @click="downloadChart"
        class="px-3 py-1 text-sm bg-blue-100 text-blue-600 rounded hover:bg-blue-200 transition-colors"
      >
        <i class="fas fa-download mr-1"></i>
        下载图表
      </button>
      <button
        @click="shareChart"
        class="px-3 py-1 text-sm bg-green-100 text-green-600 rounded hover:bg-green-200 transition-colors"
      >
        <i class="fas fa-share mr-1"></i>
        分享图表
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  type ChartConfiguration
} from 'chart.js'
import type { ChartDataPoint } from '@/types'

// 注册Chart.js组件
ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend)

interface Props {
  data: ChartDataPoint[]
  title?: string
  description?: string
  height?: number
  showActions?: boolean
  horizontal?: boolean
  stacked?: boolean
  animated?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  height: 300,
  showActions: true,
  horizontal: false,
  stacked: false,
  animated: true
})

const emit = defineEmits<{
  download: [canvas: HTMLCanvasElement]
  share: [dataUrl: string]
}>()

const chartCanvas = ref<HTMLCanvasElement>()
let chartInstance: ChartJS | null = null

onMounted(async () => {
  await nextTick()
  initChart()
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.destroy()
  }
})

watch(() => props.data, () => {
  updateChart()
}, { deep: true })

const initChart = () => {
  if (!chartCanvas.value || !props.data.length) return

  const ctx = chartCanvas.value.getContext('2d')
  if (!ctx) return

  const config: ChartConfiguration = {
    type: props.horizontal ? 'bar' : 'bar',
    data: {
      labels: props.data.map(item => item.label),
      datasets: [{
        label: '得分',
        data: props.data.map(item => item.value),
        backgroundColor: props.data.map(item => item.color || '#3b82f6'),
        borderColor: props.data.map(item => item.color || '#3b82f6'),
        borderWidth: 1,
        borderRadius: 4,
        borderSkipped: false
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      indexAxis: props.horizontal ? 'y' : 'x',
      animation: props.animated ? {
        duration: 1000,
        easing: 'easeInOutQuart'
      } : false,
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          titleColor: 'white',
          bodyColor: 'white',
          borderColor: 'rgba(255, 255, 255, 0.1)',
          borderWidth: 1,
          cornerRadius: 8,
          displayColors: false,
          callbacks: {
            label: (context) => {
              const dataPoint = props.data[context.dataIndex]
              return `${dataPoint.label}: ${context.parsed.y || context.parsed.x}`
            }
          }
        }
      },
      scales: {
        x: {
          beginAtZero: true,
          grid: {
            color: 'rgba(0, 0, 0, 0.1)'
          },
          ticks: {
            color: '#6b7280'
          }
        },
        y: {
          beginAtZero: true,
          grid: {
            color: 'rgba(0, 0, 0, 0.1)'
          },
          ticks: {
            color: '#6b7280'
          }
        }
      }
    }
  }

  chartInstance = new ChartJS(ctx, config)
}

const updateChart = () => {
  if (!chartInstance) return

  chartInstance.data.labels = props.data.map(item => item.label)
  chartInstance.data.datasets[0].data = props.data.map(item => item.value)
  chartInstance.data.datasets[0].backgroundColor = props.data.map(item => item.color || '#3b82f6')
  chartInstance.data.datasets[0].borderColor = props.data.map(item => item.color || '#3b82f6')
  
  chartInstance.update('active')
}

const downloadChart = () => {
  if (!chartCanvas.value) return
  emit('download', chartCanvas.value)
}

const shareChart = () => {
  if (!chartCanvas.value) return
  const dataUrl = chartCanvas.value.toDataURL('image/png')
  emit('share', dataUrl)
}
</script>

<style scoped>
.bar-chart-container {
  @apply w-full;
}

.chart-wrapper {
  @apply relative w-full;
}

.chart-header {
  @apply text-center;
}

.chart-actions {
  @apply flex flex-wrap gap-2;
}

@media (max-width: 640px) {
  .chart-actions {
    @apply flex-col items-center;
  }
}
</style>
