<template>
  <div class="bg-white rounded-2xl shadow-lg p-6">
    <div class="flex items-center justify-between mb-6">
      <h3 class="text-lg font-semibold text-gray-800">得分详情</h3>
      <div class="flex space-x-2">
        <button
          @click="$emit('view-chart')"
          class="text-sm text-blue-600 hover:text-blue-700 transition-colors"
          title="查看图表"
        >
          <i class="fas fa-chart-bar"></i>
        </button>
        <button
          @click="$emit('export')"
          class="text-sm text-gray-600 hover:text-gray-700 transition-colors"
          title="导出数据"
        >
          <i class="fas fa-download"></i>
        </button>
      </div>
    </div>

    <div class="space-y-4">
      <!-- 焦虑依恋得分 -->
      <div class="flex items-center justify-between p-4 bg-red-50 rounded-lg">
        <div class="flex items-center space-x-3">
          <div class="w-3 h-3 bg-red-500 rounded-full"></div>
          <div>
            <span class="font-medium text-gray-800">焦虑依恋</span>
            <p class="text-xs text-gray-600">对关系的担忧程度</p>
          </div>
        </div>
        <div class="text-right">
          <div class="text-xl font-bold text-red-600">{{ scores.anxious.toFixed(1) }}</div>
          <div class="text-xs text-gray-500">第{{ percentiles.anxious }}百分位</div>
        </div>
      </div>

      <!-- 回避依恋得分 -->
      <div class="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
        <div class="flex items-center space-x-3">
          <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
          <div>
            <span class="font-medium text-gray-800">回避依恋</span>
            <p class="text-xs text-gray-600">回避亲密的倾向</p>
          </div>
        </div>
        <div class="text-right">
          <div class="text-xl font-bold text-blue-600">{{ scores.avoidant.toFixed(1) }}</div>
          <div class="text-xs text-gray-500">第{{ percentiles.avoidant }}百分位</div>
        </div>
      </div>

      <!-- 安全依恋得分 -->
      <div class="flex items-center justify-between p-4 bg-green-50 rounded-lg">
        <div class="flex items-center space-x-3">
          <div class="w-3 h-3 bg-green-500 rounded-full"></div>
          <div>
            <span class="font-medium text-gray-800">安全依恋</span>
            <p class="text-xs text-gray-600">关系中的安全感</p>
          </div>
        </div>
        <div class="text-right">
          <div class="text-xl font-bold text-green-600">{{ scores.secure.toFixed(1) }}</div>
          <div class="text-xs text-gray-500">第{{ percentiles.secure }}百分位</div>
        </div>
      </div>
    </div>

    <!-- 可靠性指标 -->
    <div class="mt-6 p-4 bg-gray-50 rounded-lg">
      <div class="flex items-center justify-between">
        <span class="text-sm text-gray-600">测评可靠性</span>
        <div class="flex items-center space-x-2">
          <div class="w-16 h-2 bg-gray-200 rounded-full overflow-hidden">
            <div 
              class="h-full bg-gradient-to-r from-yellow-400 to-green-500 transition-all duration-500"
              :style="{ width: `${reliability * 100}%` }"
            ></div>
          </div>
          <span class="text-sm font-medium text-gray-800">
            {{ (reliability * 100).toFixed(0) }}%
          </span>
        </div>
      </div>
      <p class="text-xs text-gray-500 mt-2">
        基于您的回答一致性和模式分析
      </p>
    </div>

    <!-- 操作按钮 -->
    <div class="mt-6 flex justify-center space-x-4">
      <button
        @click="$emit('compare')"
        class="text-sm text-blue-600 hover:text-blue-700 transition-colors"
      >
        <i class="fas fa-balance-scale mr-1"></i>
        对比分析
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { AttachmentScores, AttachmentPercentiles } from '@/types'

interface Props {
  scores: AttachmentScores
  percentiles: AttachmentPercentiles
  reliability: number
}

defineProps<Props>()

defineEmits<{
  'view-chart': []
  'compare': []
  'export': []
}>()
</script>