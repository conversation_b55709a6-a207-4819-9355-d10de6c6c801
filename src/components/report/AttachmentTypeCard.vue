<template>
  <div class="bg-white rounded-2xl shadow-xl p-8 mb-8">
    <div class="text-center mb-8">
      <div 
        :class="[
          'w-24 h-24 rounded-full flex items-center justify-center mx-auto mb-4',
          attachmentDescription.bgColor || 'bg-blue-100'
        ]"
      >
        <i 
          :class="[
            'text-3xl',
            attachmentDescription.icon || 'fas fa-heart',
            attachmentDescription.color || 'text-blue-600'
          ]"
        ></i>
      </div>
      <h2 class="text-2xl font-semibold text-gray-800 mb-2">
        {{ attachmentDescription.title }}
      </h2>
      <p class="text-gray-600 mb-4">
        {{ attachmentDescription.shortDescription }}
      </p>
      <div class="flex items-center justify-center space-x-2">
        <span class="text-sm text-gray-500">置信度:</span>
        <span class="font-semibold text-blue-600">{{ confidence }}%</span>
      </div>
    </div>

    <div class="space-y-6">
      <div>
        <h3 class="text-lg font-semibold text-gray-800 mb-4">主要特征</h3>
        <div class="grid md:grid-cols-2 gap-4">
          <div 
            v-for="(characteristic, index) in displayedCharacteristics" 
            :key="index"
            class="flex items-start space-x-3"
          >
            <i class="fas fa-check text-green-500 mt-1 flex-shrink-0"></i>
            <span class="text-gray-600 text-sm">{{ characteristic }}</span>
          </div>
        </div>
      </div>

      <div class="flex flex-wrap gap-4 justify-center pt-6">
        <button
          v-if="!isUnlocked"
          @click="$emit('unlock-detailed')"
          class="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-full font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-300 transform hover:scale-105"
        >
          <i class="fas fa-unlock mr-2"></i>
          解锁详细报告
        </button>
        <button
          v-else
          @click="$emit('view-detailed')"
          class="bg-green-600 text-white px-6 py-3 rounded-full font-semibold hover:bg-green-700 transition-colors"
        >
          <i class="fas fa-eye mr-2"></i>
          查看详细报告
        </button>
        <button
          @click="$emit('share')"
          class="bg-gray-600 text-white px-6 py-3 rounded-full font-semibold hover:bg-gray-700 transition-colors"
        >
          <i class="fas fa-share mr-2"></i>
          分享报告
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { AttachmentTypeDescription, AttachmentStyle } from '@/types'

interface Props {
  attachmentType: AttachmentStyle
  description: AttachmentTypeDescription
  confidence: number
  isUnlocked: boolean
}

const props = defineProps<Props>()

defineEmits<{
  'unlock-detailed': []
  'view-detailed': []
  'share': []
}>()

const attachmentDescription = computed(() => props.description)

const displayedCharacteristics = computed(() => {
  return props.description.characteristics?.slice(0, 6) || []
})
</script>