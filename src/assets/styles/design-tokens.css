/* ECR心理测评系统 - 设计系统规范 */

:root {
  /* ========== 色彩系统 ========== */
  
  /* 主色调 - 蓝紫渐变 */
  --color-primary-50: #f0f4ff;
  --color-primary-100: #e0e9ff;
  --color-primary-200: #c7d6fe;
  --color-primary-300: #a5b8fc;
  --color-primary-400: #8b9cf7;
  --color-primary-500: #667eea;
  --color-primary-600: #5a6fd8;
  --color-primary-700: #4c5bc5;
  --color-primary-800: #3d4ba0;
  --color-primary-900: #364080;

  /* 次要色调 - 紫色 */
  --color-secondary-50: #faf5ff;
  --color-secondary-100: #f3e8ff;
  --color-secondary-200: #e9d5ff;
  --color-secondary-300: #d8b4fe;
  --color-secondary-400: #c084fc;
  --color-secondary-500: #a855f7;
  --color-secondary-600: #9333ea;
  --color-secondary-700: #7c3aed;
  --color-secondary-800: #6b21a8;
  --color-secondary-900: #581c87;

  /* 中性色 */
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;

  /* 语义色彩 */
  --color-success-50: #f0fdf4;
  --color-success-500: #22c55e;
  --color-success-600: #16a34a;
  --color-success-700: #15803d;

  --color-warning-50: #fffbeb;
  --color-warning-500: #f59e0b;
  --color-warning-600: #d97706;
  --color-warning-700: #b45309;

  --color-error-50: #fef2f2;
  --color-error-500: #ef4444;
  --color-error-600: #dc2626;
  --color-error-700: #b91c1c;

  --color-info-50: #eff6ff;
  --color-info-500: #3b82f6;
  --color-info-600: #2563eb;
  --color-info-700: #1d4ed8;

  /* ========== 字体系统 ========== */
  
  /* 字体族 */
  --font-family-sans: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Hiragino Sans GB', 'Microsoft YaHei', Roboto, sans-serif;
  --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

  /* 字体大小 */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  --font-size-5xl: 3rem;      /* 48px */
  --font-size-6xl: 3.75rem;   /* 60px */

  /* 字体粗细 */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;

  /* 行高 */
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;

  /* ========== 间距系统 ========== */
  
  --spacing-0: 0;
  --spacing-1: 0.25rem;   /* 4px */
  --spacing-2: 0.5rem;    /* 8px */
  --spacing-3: 0.75rem;   /* 12px */
  --spacing-4: 1rem;      /* 16px */
  --spacing-5: 1.25rem;   /* 20px */
  --spacing-6: 1.5rem;    /* 24px */
  --spacing-8: 2rem;      /* 32px */
  --spacing-10: 2.5rem;   /* 40px */
  --spacing-12: 3rem;     /* 48px */
  --spacing-16: 4rem;     /* 64px */
  --spacing-20: 5rem;     /* 80px */
  --spacing-24: 6rem;     /* 96px */
  --spacing-32: 8rem;     /* 128px */

  /* ========== 圆角系统 ========== */
  
  --radius-none: 0;
  --radius-sm: 0.125rem;   /* 2px */
  --radius-base: 0.25rem;  /* 4px */
  --radius-md: 0.375rem;   /* 6px */
  --radius-lg: 0.5rem;     /* 8px */
  --radius-xl: 0.75rem;    /* 12px */
  --radius-2xl: 1rem;      /* 16px */
  --radius-3xl: 1.5rem;    /* 24px */
  --radius-full: 9999px;

  /* ========== 阴影系统 ========== */
  
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

  /* ========== 过渡动画 ========== */
  
  --transition-fast: 150ms ease-in-out;
  --transition-base: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;

  /* 缓动函数 */
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);

  /* ========== Z-index 层级 ========== */
  
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;
  --z-index-toast: 1080;

  /* ========== 断点系统 ========== */
  
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;

  /* ========== 组件特定变量 ========== */
  
  /* 按钮 */
  --button-height-sm: 2rem;     /* 32px */
  --button-height-md: 2.5rem;   /* 40px */
  --button-height-lg: 3rem;     /* 48px */
  --button-padding-x-sm: 0.75rem;
  --button-padding-x-md: 1rem;
  --button-padding-x-lg: 1.5rem;

  /* 输入框 */
  --input-height-sm: 2rem;
  --input-height-md: 2.5rem;
  --input-height-lg: 3rem;
  --input-padding-x: 0.75rem;
  --input-border-width: 1px;

  /* 卡片 */
  --card-padding: 1.5rem;
  --card-border-width: 1px;
  --card-border-color: var(--color-gray-200);

  /* 模态框 */
  --modal-backdrop-color: rgba(0, 0, 0, 0.5);
  --modal-border-radius: var(--radius-lg);
  --modal-padding: 1.5rem;

  /* ========== 主题变量 ========== */
  
  /* 背景色 */
  --bg-primary: #ffffff;
  --bg-secondary: var(--color-gray-50);
  --bg-tertiary: var(--color-gray-100);
  --bg-overlay: rgba(0, 0, 0, 0.5);

  /* 文字色 */
  --text-primary: var(--color-gray-900);
  --text-secondary: var(--color-gray-700);
  --text-tertiary: var(--color-gray-500);
  --text-placeholder: var(--color-gray-400);
  --text-disabled: var(--color-gray-300);
  --text-inverse: #ffffff;

  /* 边框色 */
  --border-primary: var(--color-gray-200);
  --border-secondary: var(--color-gray-300);
  --border-focus: var(--color-primary-500);
  --border-error: var(--color-error-500);
  --border-success: var(--color-success-500);

  /* 渐变 */
  --gradient-primary: linear-gradient(135deg, var(--color-primary-500) 0%, var(--color-secondary-500) 100%);
  --gradient-secondary: linear-gradient(135deg, var(--color-gray-50) 0%, var(--color-gray-100) 100%);
  --gradient-hero: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* ========== 暗色主题 ========== */
[data-theme="dark"] {
  /* 背景色 */
  --bg-primary: var(--color-gray-900);
  --bg-secondary: var(--color-gray-800);
  --bg-tertiary: var(--color-gray-700);

  /* 文字色 */
  --text-primary: var(--color-gray-100);
  --text-secondary: var(--color-gray-300);
  --text-tertiary: var(--color-gray-400);

  /* 边框色 */
  --border-primary: var(--color-gray-700);
  --border-secondary: var(--color-gray-600);

  /* 卡片边框 */
  --card-border-color: var(--color-gray-700);
}

/* ========== 工具类 ========== */

/* 渐变背景 */
.gradient-primary {
  background: var(--gradient-primary);
}

.gradient-secondary {
  background: var(--gradient-secondary);
}

.gradient-hero {
  background: var(--gradient-hero);
}

/* 文字渐变 */
.text-gradient-primary {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 阴影工具类 */
.shadow-custom {
  box-shadow: var(--shadow-lg);
}

.shadow-custom-xl {
  box-shadow: var(--shadow-xl);
}

/* 过渡工具类 */
.transition-fast {
  transition: all var(--transition-fast);
}

.transition-base {
  transition: all var(--transition-base);
}

.transition-slow {
  transition: all var(--transition-slow);
}
