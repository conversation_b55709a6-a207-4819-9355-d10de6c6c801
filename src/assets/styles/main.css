@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* 导入设计系统 */
@import './design-tokens.css';

/* 自定义CSS变量 */
:root {
  /* 主色调 */
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --primary-color: #667eea;
  --primary-dark: #5a6fd8;

  /* 文字颜色 */
  --text-primary: #2c3e50;
  --text-secondary: #495057;
  --text-muted: #6c757d;
  --text-light: #adb5bd;

  /* 背景色 */
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #e9ecef;
  --bg-gradient: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);

  /* 边框和分割线 */
  --border-light: rgba(0, 0, 0, 0.05);
  --border-medium: rgba(0, 0, 0, 0.1);
  --border-primary: rgba(102, 126, 234, 0.2);

  /* 阴影 */
  --shadow-light: 0 3px 10px rgba(0, 0, 0, 0.05);
  --shadow-medium: 0 6px 20px rgba(0, 0, 0, 0.08);
  --shadow-heavy: 0 10px 30px rgba(0, 0, 0, 0.12);

  /* 状态色 */
  --success-color: #27ae60;
  --warning-color: #f39c12;
  --danger-color: #e74c3c;
  --info-color: #3498db;
}

/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family:
    'PingFang SC',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    'Hiragino Sans GB',
    'Microsoft YaHei',
    Roboto,
    sans-serif;
  font-feature-settings:
    'liga' 1,
    'kern' 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 自定义工具类 */
.gradient-bg {
  background: var(--bg-gradient);
}

.primary-gradient {
  background: var(--primary-gradient);
}

.shadow-custom {
  box-shadow: var(--shadow-medium);
}

/* 动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from {
  transform: translateX(100%);
}

.slide-leave-to {
  transform: translateX(-100%);
}
