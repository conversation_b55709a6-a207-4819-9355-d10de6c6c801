// 依恋类型描述常量
import type { AttachmentStyle } from '@/types'

export interface AttachmentDescription {
  title: string
  subtitle: string
  summary: string
  characteristics: string[]
  strengths: string[]
  challenges: string[]
  relationshipPatterns: string[]
  growthSuggestions: string[]
  compatibilityNotes: string[]
}

// 依恋类型描述
export const ATTACHMENT_DESCRIPTIONS: Record<AttachmentStyle, AttachmentDescription> = {
  secure: {
    title: '安全型依恋',
    subtitle: '您拥有健康稳定的依恋模式',
    summary:
      '您在亲密关系中感到安全和舒适，能够平衡独立性和亲密性。您相信自己值得被爱，也相信他人基本上是可信赖和支持性的。这种依恋风格通常与健康、满意的人际关系相关联。',
    characteristics: [
      '在关系中感到安全和舒适',
      '能够有效表达情感需求和边界',
      '既重视亲密连接也尊重个人空间',
      '信任伴侣并支持其独立发展',
      '具有良好的情绪调节和沟通能力',
      '在冲突中寻求建设性的解决方案',
      '对自己和他人都有积极正面的看法'
    ],
    strengths: [
      '情绪稳定性高，能够有效管理压力',
      '沟通技巧良好，善于表达和倾听',
      '具有高度的同理心和情感敏感性',
      '能够在关系中保持健康的边界',
      '适应性强，面对变化时较为灵活',
      '自我价值感稳定，不过度依赖他人认可',
      '能够给予和接受支持'
    ],
    challenges: [
      '可能对不安全依恋的伴侣感到困惑',
      '有时可能低估关系中的问题',
      '在面对高度冲突时可能感到不知所措',
      '可能需要学习如何更好地支持不安全依恋的伴侣'
    ],
    relationshipPatterns: [
      '倾向于建立长期稳定的关系',
      '在关系中既亲密又独立',
      '能够有效处理关系冲突和分歧',
      '支持伴侣的个人成长和目标',
      '在关系中保持开放和诚实的沟通',
      '能够从关系经验中学习和成长'
    ],
    growthSuggestions: [
      '继续保持开放和诚实的沟通方式',
      '在关系中保持适度的独立性和个人兴趣',
      '学习如何更好地支持不安全依恋的伴侣',
      '在冲突时保持耐心和理解',
      '分享您的关系智慧，帮助身边的朋友',
      '定期反思和评估关系的健康状况'
    ],
    compatibilityNotes: [
      '与各种依恋类型都能建立良好关系',
      '能够帮助不安全依恋的伴侣获得安全感',
      '在关系中起到稳定和支持的作用',
      '最适合与同样安全型或正在成长的伴侣建立关系'
    ]
  },

  anxious: {
    title: '焦虑型依恋',
    subtitle: '您在关系中渴望亲密但又担心失去',
    summary:
      '您对亲密关系高度重视和敏感，渴望与伴侣建立深度连接，但同时也容易担心被抛弃或不被爱。您可能需要频繁的关爱确认，并且对关系中的细微变化特别敏感。这种模式源于对自我价值的不确定和对他人可依赖性的担忧。',
    characteristics: [
      '对关系高度敏感和专注',
      '容易担心被抛弃、拒绝或不被重视',
      '需要频繁的关爱确认和安慰',
      '情绪波动较大，特别是在关系问题上',
      '倾向于过度分析伴侣的言行和情绪',
      '在关系中可能表现出粘人的行为',
      '对分离和距离感到焦虑不安'
    ],
    strengths: [
      '对关系高度投入和忠诚',
      '情感表达丰富，善于感知他人情绪',
      '具有强烈的同理心和关怀能力',
      '在关系中愿意付出很多努力',
      '对伴侣的需求非常敏感和关注',
      '能够建立深度的情感连接',
      '具有很强的直觉和洞察力'
    ],
    challenges: [
      '容易因担心失去关系而产生焦虑',
      '可能过度依赖伴侣的关注和认可',
      '在面对分离或冲突时情绪反应强烈',
      '容易将伴侣的正常行为解读为拒绝信号',
      '可能因为害怕冲突而压抑自己的需求',
      '自我价值感容易受关系状况影响',
      '可能表现出控制或监视行为'
    ],
    relationshipPatterns: [
      '在关系初期表现得特别热情和投入',
      '经常寻求伴侣的关注和确认',
      '对关系状况的变化极其敏感',
      '可能会测试伴侣的忠诚度和承诺',
      '在冲突后需要大量的安抚和保证',
      '倾向于将关系放在生活的中心位置'
    ],
    growthSuggestions: [
      '学习自我安抚和情绪调节技巧',
      '培养独立的兴趣爱好和社交关系',
      '练习识别和挑战负面的思维模式',
      '学会表达需求而不是通过行为暗示',
      '建立自我价值感，减少对外界认可的依赖',
      '考虑寻求专业心理咨询师的帮助',
      '学习健康的沟通和冲突解决技巧'
    ],
    compatibilityNotes: [
      '与安全型伴侣的关系最为和谐稳定',
      '需要避免与回避型伴侣形成追逐-逃避的恶性循环',
      '与同样焦虑型的伴侣可能产生情绪放大效应',
      '需要伴侣有足够的耐心和理解能力'
    ]
  },

  avoidant: {
    title: '回避型依恋',
    subtitle: '您重视独立但在情感亲密上较为保守',
    summary:
      '您高度重视个人的独立性和自主性，倾向于在情感表达和亲密关系上保持一定距离。您可能担心过度的依赖会限制自由或导致伤害，因此更喜欢自力更生。这种模式通常源于对他人可靠性的怀疑和对自我保护的需要。',
    characteristics: [
      '高度重视个人独立性和自主权',
      '在情感表达和分享上较为保守',
      '倾向于避免过度亲密和依赖',
      '更喜欢理性而非情感化的交流方式',
      '在压力下倾向于独自处理问题',
      '对他人的情感需求可能反应迟钝',
      '不太愿意寻求他人的帮助和支持'
    ],
    strengths: [
      '具有很强的独立性和自力更生能力',
      '情绪相对稳定，不易受他人影响',
      '理性思考能力强，善于解决实际问题',
      '在工作和个人成就上通常表现优秀',
      '能够给予伴侣足够的个人空间',
      '在危机时刻能够保持冷静和客观',
      '具有很强的自我控制能力'
    ],
    challenges: [
      '难以表达深层的情感需求和脆弱',
      '可能让伴侣感到被拒绝或不被需要',
      '在面对他人的情感需求时感到不知所措',
      '倾向于通过工作或其他活动逃避亲密',
      '可能缺乏同理心和情感敏感性',
      '在冲突时容易采取冷战或回避策略',
      '难以接受他人的关怀和支持'
    ],
    relationshipPatterns: [
      '倾向于保持一定的情感距离',
      '更注重关系的实用性而非情感连接',
      '可能会通过工作或兴趣转移注意力',
      '在关系变得过于亲密时会退缩',
      '倾向于避免深入的情感对话',
      '可能会下意识地寻找关系中的缺陷'
    ],
    growthSuggestions: [
      '练习识别和表达自己的情感需求',
      '逐步增加与伴侣的情感分享和交流',
      '学会接受他人的关怀和支持',
      '探索对亲密关系恐惧的根源',
      '培养情感敏感性和同理心',
      '学习健康的依赖和相互支持',
      '在安全的环境中练习脆弱性表达'
    ],
    compatibilityNotes: [
      '与安全型伴侣能够逐步建立信任和亲密',
      '与焦虑型伴侣容易形成追逐-逃避的冲突模式',
      '与同样回避型的伴侣可能缺乏深度情感连接',
      '需要伴侣有足够的耐心和包容性'
    ]
  },

  disorganized: {
    title: '混乱型依恋',
    subtitle: '您在关系中体验到复杂和矛盾的情感',
    summary:
      '您在亲密关系中可能同时体验到强烈的渴望和恐惧，既想要建立深度连接又害怕受到伤害。您的情绪和行为模式可能不够一致，在不同情境下表现出不同的依恋行为。这种模式通常源于早期复杂的依恋经历，需要更多的自我觉察和专业支持。',
    characteristics: [
      '在关系中体验到矛盾和冲突的情感',
      '既渴望亲密连接又害怕被伤害',
      '情绪和行为模式相对不稳定',
      '可能在不同情境下表现出不同的依恋行为',
      '对关系的安全性感到不确定',
      '可能同时表现出焦虑和回避的特征',
      '在压力下容易出现极端的情绪反应'
    ],
    strengths: [
      '对人际关系有深刻的洞察和敏感性',
      '具有丰富的情感体验和表达能力',
      '在经历成长后能够建立深度的连接',
      '对他人的痛苦和挣扎有很强的同理心',
      '具有很强的适应性和生存能力',
      '能够在复杂情境中找到创新的解决方案',
      '具有独特的视角和创造性思维'
    ],
    challenges: [
      '情绪调节能力相对较弱',
      '在关系中可能表现出不一致的行为',
      '容易被过去的创伤经历影响当前关系',
      '可能对伴侣的意图产生误解或过度解读',
      '在面对冲突时容易出现极端反应',
      '自我认知和情绪识别能力需要提升',
      '可能需要更长时间来建立安全的关系'
    ],
    relationshipPatterns: [
      '关系中可能出现"热烈-冷淡"的循环模式',
      '容易被触发进入防御或攻击状态',
      '可能会测试伴侣的耐心和承诺',
      '在关系中寻求安全感但又害怕脆弱',
      '可能表现出自我破坏的行为模式',
      '需要大量的安全感确认和支持'
    ],
    growthSuggestions: [
      '寻求专业心理咨询师或治疗师的帮助',
      '学习情绪识别和调节的具体技巧',
      '探索和处理早期依恋创伤的影响',
      '建立稳定的自我关怀和安抚习惯',
      '在治疗关系中练习安全的依恋体验',
      '学习正念和冥想来提高自我觉察',
      '逐步建立对自己和他人的信任'
    ],
    compatibilityNotes: [
      '最需要安全型伴侣提供稳定的支持和理解',
      '在关系中需要更多的耐心、包容和一致性',
      '建议在个人治疗和成长达到一定程度后再建立长期关系',
      '需要伴侣具备较强的情绪稳定性和支持能力'
    ]
  }
}

// 简化版描述（用于快速展示）
export const ATTACHMENT_SUMMARIES: Record<AttachmentStyle, string> = {
  secure:
    '您具有安全型依恋风格，在关系中感到安全舒适，能够平衡独立性和亲密性，拥有健康稳定的人际关系模式。',
  anxious: '您具有焦虑型依恋风格，渴望与伴侣建立深度连接，但容易担心被抛弃，需要频繁的关爱确认。',
  avoidant: '您具有回避型依恋风格，高度重视个人独立性，在情感表达上较为保守，倾向于避免过度亲密。',
  disorganized:
    '您具有混乱型依恋风格，在关系中体验到矛盾情感，既渴望亲密又害怕受伤，需要更多自我觉察和成长。'
}

// 依恋类型标签和颜色
export const ATTACHMENT_LABELS: Record<
  AttachmentStyle,
  { label: string; color: string; bgColor: string }
> = {
  secure: {
    label: '安全型',
    color: 'text-green-700',
    bgColor: 'bg-green-100'
  },
  anxious: {
    label: '焦虑型',
    color: 'text-orange-700',
    bgColor: 'bg-orange-100'
  },
  avoidant: {
    label: '回避型',
    color: 'text-blue-700',
    bgColor: 'bg-blue-100'
  },
  disorganized: {
    label: '混乱型',
    color: 'text-purple-700',
    bgColor: 'bg-purple-100'
  }
}

// 依恋类型解释性文本
export const ATTACHMENT_EXPLANATIONS = {
  scoreInterpretation:
    '您的依恋风格反映了您在亲密关系中的情感模式和行为倾向。这些模式通常在早期生活经历中形成，但可以通过自我觉察和努力而改变。',
  dimensionExplanation: {
    anxiety: '焦虑维度反映您对被抛弃或拒绝的担心程度，以及对关系安全性的需求强度。',
    avoidance: '回避维度反映您对情感亲密的舒适程度，以及在关系中保持独立性的倾向。'
  },
  growthMessage:
    '请记住，依恋风格并非固定不变。通过自我觉察、学习健康的关系技巧，以及在必要时寻求专业帮助，您可以发展出更加安全和满意的关系模式。'
}
