# 💳 支付问题排查指南

如果在支付过程中遇到问题，请按照以下步骤进行排查和解决。

## 🔄 支付完成后没有自动跳转？

### 方法1：使用"检查支付状态"按钮

1. **在支付模态框中**：
   - 完成支付后，返回原页面
   - 点击"检查支付状态"按钮
   - 系统会自动检查并显示详细报告

2. **在结果页面中**：
   - 点击"我已完成支付"按钮
   - 系统会验证支付状态并显示报告

### 方法2：手动刷新页面

1. 完成支付后，刷新浏览器页面（F5或Ctrl+R）
2. 如果有支付数据，系统会自动检测并显示报告

### 方法3：使用调试工具

1. 按F12打开开发者工具
2. 在控制台输入：`debugCheckStatus()`
3. 查看输出信息，确认支付数据是否存在

## 🐛 常见问题及解决方案

### Q1: 点击"前往支付"没有反应
**可能原因**：
- 浏览器阻止了弹窗
- Payment Link配置错误

**解决方案**：
1. 允许浏览器弹窗
2. 检查控制台是否有错误信息
3. 确认Payment Link URL是否正确

### Q2: 支付成功但数据丢失
**可能原因**：
- localStorage被清除
- 浏览器隐私设置过严

**解决方案**：
1. 点击"检查支付状态"按钮
2. 如果还是没有数据，使用调试模式：
   ```javascript
   // 在控制台输入
   debugPaymentSuccess()
   ```

### Q3: 在本地文件系统测试（file://协议）
**问题**：
- localStorage可能不工作
- 跨域限制

**解决方案**：
1. 使用本地服务器：
   ```bash
   # Python
   python -m http.server 8000
   
   # Node.js
   npx serve
   
   # PHP
   php -S localhost:8000
   ```
2. 然后访问 `http://localhost:8000`

### Q4: 移动端支付问题
**可能原因**：
- 移动浏览器的弹窗限制
- 支付页面适配问题

**解决方案**：
1. 在移动端，支付链接会在同一窗口打开
2. 支付完成后手动返回原页面
3. 点击"我已完成支付"按钮

## 🔧 调试工具使用

### 1. 检查当前状态
```javascript
// 在浏览器控制台输入
debugCheckStatus()
```

### 2. 查看localStorage数据
```javascript
// 检查支付数据
console.log('pendingResults:', localStorage.getItem('pendingResults'));
console.log('backupResults:', localStorage.getItem('ecrBackupResults'));
```

### 3. 手动设置支付成功状态
```javascript
// 紧急情况下的手动设置（仅用于调试）
localStorage.setItem('pendingResults', JSON.stringify({
    anxietyScore: '2.4',
    avoidanceScore: '3.0',
    attachmentType: '安全型依恋',
    paymentInitiated: true,
    timestamp: Date.now()
}));
// 然后点击"检查支付状态"按钮
```

## 📱 不同环境的使用建议

### 桌面端浏览器
- **推荐**：Chrome、Firefox、Safari、Edge
- **支付方式**：新窗口打开支付页面
- **返回方式**：支付完成后返回原窗口，点击按钮

### 移动端浏览器
- **推荐**：Safari（iOS）、Chrome（Android）
- **支付方式**：同窗口跳转到支付页面
- **返回方式**：支付完成后手动返回，点击按钮

### 本地开发环境
- **必须使用**：HTTP服务器（不能直接打开HTML文件）
- **推荐端口**：8000、3000、8080
- **测试卡号**：4242 4242 4242 4242

## 🚨 紧急解决方案

如果所有方法都不工作，可以使用以下紧急方案：

### 方案1：使用调试模式
1. 打开支付模态框
2. 点击"调试：模拟支付成功"按钮
3. 查看示例详细报告

### 方案2：重新测评
1. 点击"重新测试"按钮
2. 重新完成测评
3. 再次尝试支付流程

### 方案3：联系技术支持
如果问题持续存在，请提供以下信息：
- 浏览器类型和版本
- 操作系统
- 控制台错误信息
- 支付流程的详细步骤

## 📞 获取帮助

- **GitHub Issues**: 在项目仓库提交问题
- **调试信息**: 使用 `debugCheckStatus()` 获取详细信息
- **Stripe支持**: 如果是支付相关问题，可联系Stripe客服

---

💡 **提示**：大多数支付问题都可以通过点击"检查支付状态"或"我已完成支付"按钮来解决！
