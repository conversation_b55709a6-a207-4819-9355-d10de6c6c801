import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { mount } from '@vue/test-utils'
import RadarChart from '@/components/charts/RadarChart.vue'
import type { ChartDataPoint } from '@/types'

// 模拟Chart.js
vi.mock('chart.js', () => {
  const Chart = vi.fn().mockImplementation(() => ({
    destroy: vi.fn(),
    update: vi.fn(),
    data: {
      labels: [],
      datasets: []
    }
  }))

  // 添加 register 静态方法
  Chart.register = vi.fn()

  return {
    Chart,
    RadialLinearScale: vi.fn(),
    PointElement: vi.fn(),
    LineElement: vi.fn(),
    Filler: vi.fn(),
    Tooltip: vi.fn(),
    Legend: vi.fn()
  }
})

// 模拟canvas context
const mockContext = {
  getContext: vi.fn(() => ({
    clearRect: vi.fn(),
    fillRect: vi.fn(),
    strokeRect: vi.fn(),
    beginPath: vi.fn(),
    moveTo: vi.fn(),
    lineTo: vi.fn(),
    stroke: vi.fn(),
    fill: vi.fn(),
    arc: vi.fn(),
    save: vi.fn(),
    restore: vi.fn(),
    translate: vi.fn(),
    rotate: vi.fn(),
    scale: vi.fn()
  }))
}

// 模拟HTMLCanvasElement
Object.defineProperty(HTMLCanvasElement.prototype, 'getContext', {
  value: mockContext.getContext
})

describe('RadarChart', () => {
  const mockData: ChartDataPoint[] = [
    { label: '焦虑依恋', value: 3.5, color: '#ef4444' },
    { label: '回避依恋', value: 2.8, color: '#3b82f6' },
    { label: '安全依恋', value: 5.2, color: '#10b981' }
  ]

  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  it('应该正确渲染组件', () => {
    const wrapper = mount(RadarChart, {
      props: {
        data: mockData,
        title: '依恋维度雷达图',
        description: '测试描述'
      }
    })

    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('canvas').exists()).toBe(true)
  })

  it('应该显示标题和描述', () => {
    const title = '依恋维度雷达图'
    const description = '测试描述'
    
    const wrapper = mount(RadarChart, {
      props: {
        data: mockData,
        title,
        description
      }
    })

    expect(wrapper.text()).toContain(title)
    expect(wrapper.text()).toContain(description)
  })

  it('应该在showActions为true时显示操作按钮', () => {
    const wrapper = mount(RadarChart, {
      props: {
        data: mockData,
        showActions: true
      }
    })

    expect(wrapper.find('button').exists()).toBe(true)
    expect(wrapper.text()).toContain('下载图表')
    expect(wrapper.text()).toContain('刷新')
  })

  it('应该在showActions为false时隐藏操作按钮', () => {
    const wrapper = mount(RadarChart, {
      props: {
        data: mockData,
        showActions: false
      }
    })

    expect(wrapper.find('.chart-actions').exists()).toBe(false)
  })

  it('应该在点击下载按钮时执行下载操作', async () => {
    const wrapper = mount(RadarChart, {
      props: {
        data: mockData,
        showActions: true
      }
    })

    const buttons = wrapper.findAll('button')
    const downloadButton = buttons.find(btn => btn.text().includes('下载图表'))
    expect(downloadButton).toBeDefined()

    // 模拟 document.createElement 和 click 方法
    const mockLink = { download: '', href: '', click: vi.fn() }
    vi.spyOn(document, 'createElement').mockReturnValue(mockLink as any)

    await downloadButton!.trigger('click')

    // 验证下载操作被调用
    expect(document.createElement).toHaveBeenCalledWith('a')
  })

  it('应该在点击刷新按钮时重新初始化图表', async () => {
    const wrapper = mount(RadarChart, {
      props: {
        data: mockData,
        showActions: true
      }
    })

    const buttons = wrapper.findAll('button')
    const refreshButton = buttons.find(btn => btn.text().includes('刷新'))
    expect(refreshButton).toBeDefined()

    // 刷新按钮应该存在并可以点击
    await refreshButton!.trigger('click')

    // 验证组件仍然存在（刷新操作完成）
    expect(wrapper.exists()).toBe(true)
  })

  it('应该正确处理空数据', () => {
    const wrapper = mount(RadarChart, {
      props: {
        data: []
      }
    })

    expect(wrapper.exists()).toBe(true)
    // 组件应该能够处理空数据而不崩溃
  })

  it('应该正确设置canvas尺寸', () => {
    const height = '400px'
    const wrapper = mount(RadarChart, {
      props: {
        data: mockData,
        height
      }
    })

    const canvas = wrapper.find('canvas')
    expect(canvas.exists()).toBe(true)
    // 在测试环境中，canvas 的尺寸由 CSS 控制
  })

  it('应该在数据变化时更新图表', async () => {
    const wrapper = mount(RadarChart, {
      props: {
        data: mockData
      }
    })

    // 更改数据
    const newData: ChartDataPoint[] = [
      { label: '焦虑依恋', value: 4.5, color: '#ef4444' },
      { label: '回避依恋', value: 3.8, color: '#3b82f6' },
      { label: '安全依恋', value: 4.2, color: '#10b981' }
    ]

    await wrapper.setProps({ data: newData })

    // 验证组件仍然正常渲染
    expect(wrapper.exists()).toBe(true)
  })

  it('应该正确处理动画设置', () => {
    // 测试启用动画
    const wrapperWithAnimation = mount(RadarChart, {
      props: {
        data: mockData,
        animated: true
      }
    })
    expect(wrapperWithAnimation.exists()).toBe(true)

    // 测试禁用动画
    const wrapperWithoutAnimation = mount(RadarChart, {
      props: {
        data: mockData,
        animated: false
      }
    })
    expect(wrapperWithoutAnimation.exists()).toBe(true)
  })

  it('应该正确处理自定义颜色', () => {
    const customData: ChartDataPoint[] = [
      { label: '测试1', value: 3.5, color: '#ff0000' },
      { label: '测试2', value: 2.8, color: '#00ff00' },
      { label: '测试3', value: 5.2, color: '#0000ff' }
    ]

    const wrapper = mount(RadarChart, {
      props: {
        data: customData
      }
    })

    expect(wrapper.exists()).toBe(true)
    // 组件应该能够处理自定义颜色
  })

  it('应该在组件卸载时清理资源', () => {
    const wrapper = mount(RadarChart, {
      props: {
        data: mockData
      }
    })

    // 卸载组件
    wrapper.unmount()

    // 验证没有错误抛出
    expect(true).toBe(true)
  })
})
