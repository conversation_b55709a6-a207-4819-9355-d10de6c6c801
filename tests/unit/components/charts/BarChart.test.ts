import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { mount } from '@vue/test-utils'
import BarChart from '@/components/charts/BarChart.vue'
import type { ChartDataPoint } from '@/types'

// 模拟Chart.js
vi.mock('chart.js', () => {
  const Chart = vi.fn().mockImplementation(() => ({
    destroy: vi.fn(),
    update: vi.fn(),
    data: {
      labels: [],
      datasets: []
    }
  }))

  // 添加 register 静态方法
  Chart.register = vi.fn()

  return {
    Chart,
    CategoryScale: vi.fn(),
    LinearScale: vi.fn(),
    BarElement: vi.fn(),
    Title: vi.fn(),
    Tooltip: vi.fn(),
    Legend: vi.fn()
  }
})

// 模拟canvas context
const mockContext = {
  getContext: vi.fn(() => ({
    clearRect: vi.fn(),
    fillRect: vi.fn(),
    strokeRect: vi.fn(),
    beginPath: vi.fn(),
    moveTo: vi.fn(),
    lineTo: vi.fn(),
    stroke: vi.fn(),
    fill: vi.fn(),
    arc: vi.fn(),
    save: vi.fn(),
    restore: vi.fn(),
    translate: vi.fn(),
    rotate: vi.fn(),
    scale: vi.fn()
  }))
}

// 模拟HTMLCanvasElement
Object.defineProperty(HTMLCanvasElement.prototype, 'getContext', {
  value: mockContext.getContext
})

describe('BarChart', () => {
  const mockData: ChartDataPoint[] = [
    { label: '焦虑依恋', value: 75, color: '#ef4444' },
    { label: '回避依恋', value: 60, color: '#3b82f6' },
    { label: '安全依恋', value: 85, color: '#10b981' }
  ]

  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  it('应该正确渲染组件', () => {
    const wrapper = mount(BarChart, {
      props: {
        data: mockData,
        title: '百分位数对比',
        description: '测试描述'
      }
    })

    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('.bar-chart-container').exists()).toBe(true)
  })

  it('应该显示标题和描述', () => {
    const title = '百分位数对比'
    const description = '测试描述'
    
    const wrapper = mount(BarChart, {
      props: {
        data: mockData,
        title,
        description
      }
    })

    expect(wrapper.text()).toContain(title)
    expect(wrapper.text()).toContain(description)
  })

  it('应该在showActions为true时显示操作按钮', () => {
    const wrapper = mount(BarChart, {
      props: {
        data: mockData,
        showActions: true
      }
    })

    expect(wrapper.find('.chart-actions').exists()).toBe(true)
    expect(wrapper.text()).toContain('下载图表')
    expect(wrapper.text()).toContain('分享图表')
  })

  it('应该在showActions为false时隐藏操作按钮', () => {
    const wrapper = mount(BarChart, {
      props: {
        data: mockData,
        showActions: false
      }
    })

    expect(wrapper.find('.chart-actions').exists()).toBe(false)
  })

  it('应该在点击下载按钮时触发download事件', async () => {
    const wrapper = mount(BarChart, {
      props: {
        data: mockData,
        showActions: true
      }
    })

    const downloadButton = wrapper.find('button:first-child')
    await downloadButton.trigger('click')

    expect(wrapper.emitted('download')).toBeTruthy()
  })

  it('应该在点击分享按钮时触发share事件', async () => {
    const wrapper = mount(BarChart, {
      props: {
        data: mockData,
        showActions: true
      }
    })

    const shareButton = wrapper.find('button:last-child')
    await shareButton.trigger('click')

    expect(wrapper.emitted('share')).toBeTruthy()
  })

  it('应该正确处理空数据', () => {
    const wrapper = mount(BarChart, {
      props: {
        data: []
      }
    })

    expect(wrapper.exists()).toBe(true)
    // 组件应该能够处理空数据而不崩溃
  })

  it('应该正确设置canvas尺寸', () => {
    const height = 400
    const wrapper = mount(BarChart, {
      props: {
        data: mockData,
        height
      }
    })

    const chartWrapper = wrapper.find('.chart-wrapper')
    expect(chartWrapper.attributes('style')).toContain(`height: ${height}px`)
  })

  it('应该在数据变化时更新图表', async () => {
    const wrapper = mount(BarChart, {
      props: {
        data: mockData
      }
    })

    // 更改数据
    const newData: ChartDataPoint[] = [
      { label: '焦虑依恋', value: 80, color: '#ef4444' },
      { label: '回避依恋', value: 65, color: '#3b82f6' },
      { label: '安全依恋', value: 90, color: '#10b981' }
    ]

    await wrapper.setProps({ data: newData })

    // 验证组件仍然正常渲染
    expect(wrapper.exists()).toBe(true)
  })

  it('应该正确处理水平/垂直方向设置', () => {
    // 测试垂直方向（默认）
    const verticalWrapper = mount(BarChart, {
      props: {
        data: mockData,
        horizontal: false
      }
    })
    expect(verticalWrapper.exists()).toBe(true)

    // 测试水平方向
    const horizontalWrapper = mount(BarChart, {
      props: {
        data: mockData,
        horizontal: true
      }
    })
    expect(horizontalWrapper.exists()).toBe(true)
  })

  it('应该正确处理堆叠设置', () => {
    // 测试非堆叠（默认）
    const nonStackedWrapper = mount(BarChart, {
      props: {
        data: mockData,
        stacked: false
      }
    })
    expect(nonStackedWrapper.exists()).toBe(true)

    // 测试堆叠
    const stackedWrapper = mount(BarChart, {
      props: {
        data: mockData,
        stacked: true
      }
    })
    expect(stackedWrapper.exists()).toBe(true)
  })

  it('应该正确处理动画设置', () => {
    // 测试启用动画（默认）
    const animatedWrapper = mount(BarChart, {
      props: {
        data: mockData,
        animated: true
      }
    })
    expect(animatedWrapper.exists()).toBe(true)

    // 测试禁用动画
    const nonAnimatedWrapper = mount(BarChart, {
      props: {
        data: mockData,
        animated: false
      }
    })
    expect(nonAnimatedWrapper.exists()).toBe(true)
  })

  it('应该在组件卸载时清理资源', () => {
    const wrapper = mount(BarChart, {
      props: {
        data: mockData
      }
    })

    // 卸载组件
    wrapper.unmount()

    // 验证没有错误抛出
    expect(true).toBe(true)
  })
})
