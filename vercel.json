{"buildCommand": "npm run build", "outputDirectory": "dist", "framework": "vite", "installCommand": "npm install", "devCommand": "npm run dev", "rewrites": [{"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "functions": {"api/create-payment.js": {"runtime": "nodejs18.x"}, "api/verify-payment.js": {"runtime": "nodejs18.x"}, "api/generate-token.js": {"runtime": "nodejs18.x"}}, "env": {"VITE_APP_TITLE": "@VITE_APP_TITLE", "VITE_STRIPE_PUBLISHABLE_KEY": "@VITE_STRIPE_PUBLISHABLE_KEY", "VITE_SENTRY_DSN": "@VITE_SENTRY_DSN", "VITE_API_BASE_URL": "@VITE_API_BASE_URL"}}