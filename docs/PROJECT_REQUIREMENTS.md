# ECR心理测评系统 - 项目需求文档 (PRD)

## 📋 项目概述

### 项目背景
ECR（亲密关系经历量表）是基于Brennan, Clark & Shaver (1998)依恋理论开发的标准化心理测评工具。本项目旨在构建一个现代化的Web端心理测评系统，为用户提供专业、便捷的依恋类型评估服务。

### 项目目标
- 提供标准化的ECR心理测评服务
- 生成专业的依恋类型分析报告
- 实现可持续的商业化运营模式
- 保障用户隐私和数据安全

### 项目定位
- **目标用户**：18-50岁关注亲密关系的成年人群
- **使用场景**：个人心理健康自测、关系咨询前评估
- **商业模式**：基础功能免费 + 详细报告付费

## 👥 用户画像与需求分析

### 主要用户群体

#### 用户群体1：关系困惑者
- **年龄**：25-35岁
- **特征**：正在经历或曾经历关系问题
- **需求**：了解自己的依恋模式，改善关系质量
- **痛点**：不知道问题出在哪里，缺乏专业指导

#### 用户群体2：心理学爱好者  
- **年龄**：22-40岁
- **特征**：对心理学有一定了解，注重自我成长
- **需求**：科学的自我认知工具，专业的分析报告
- **痛点**：网上测试不够专业，缺乏理论依据

#### 用户群体3：专业人士
- **年龄**：25-45岁
- **特征**：心理咨询师、HR、教育工作者
- **需求**：可信赖的评估工具，用于专业工作
- **痛点**：现有工具操作繁琐，报告不够详细

### 核心用户故事

```
作为一个正在经历关系困扰的用户
我希望了解自己在亲密关系中的行为模式
以便我能够有针对性地改善关系质量

作为一个心理学爱好者  
我希望获得基于权威理论的专业测评
以便我能够更科学地认识自己

作为一个心理咨询师
我希望有一个便捷的评估工具
以便我能够更好地了解来访者的依恋模式
```

## 🎯 功能需求规格

### 1. 核心测评功能

#### 1.1 测评系统
**优先级：P0**

- **功能描述**：提供标准化的36题ECR量表测评
- **具体要求**：
  - 题目内容严格遵循Brennan等人(1998)的标准版本
  - 采用7点李克特量表（1=非常不赞成，7=非常赞成）
  - 包含反向计分题目的正确处理
  - 支持答题进度保存和恢复
  - 实现答题时间统计

**验收标准**：
- 36题内容与原版量表一致
- 反向计分算法正确
- 答题流程流畅，用户体验良好
- 支持中途退出和续答

#### 1.2 计分算法
**优先级：P0**

- **功能描述**：基于标准化公式计算依恋焦虑和回避得分
- **算法要求**：
  - 依恋焦虑：18道题的平均分
  - 依恋回避：18道题的平均分  
  - 反向题目需要进行8减法处理
  - 分数精确到小数点后1位

**验收标准**：
- 计分结果与标准算法一致
- 边界值处理正确
- 异常输入处理完善

#### 1.3 依恋类型判定
**优先级：P0**

- **功能描述**：根据双维度得分确定四种依恋类型
- **判定规则**：
  - 安全型：焦虑<4 且 回避<4
  - 焦虑型：焦虑≥4 且 回避<4  
  - 回避型：焦虑<4 且 回避≥4
  - 混乱型：焦虑≥4 且 回避≥4

**验收标准**：
- 类型判定逻辑正确
- 边界值（4.0）处理准确
- 结果展示清晰易懂

### 2. 报告生成功能

#### 2.1 基础报告（免费）
**优先级：P0**

- **包含内容**：
  - 依恋类型名称和简要描述
  - 双维度得分展示
  - 3-5个关键特征
  - 基础建议（2-3条）
  - 重要提醒（非诊断声明）

**设计要求**：
- 界面简洁清晰
- 重点信息突出显示
- 适配移动端浏览

#### 2.2 详细报告（付费）
**优先级：P0**

- **包含内容**：
  - 完整的基础报告内容
  - 详细的心理学解读（800字以上）
  - 个性化改善建议（5个维度）
  - 关系发展指导
  - 专业的可视化图表
  - 支持下载为PDF/图片

**设计要求**：
- 报告专业性强，具备收藏价值
- 排版美观，适合打印和分享
- 包含个性化元素（姓名、日期等）

### 3. 可视化展示功能

#### 3.1 得分图表
**优先级：P1**

- **图表类型**：
  - 条形图：对比焦虑和回避得分
  - 四象限图：展示用户在依恋模型中的位置
  - 雷达图：多维度特征展示

**技术要求**：
- 使用现代图表库（Chart.js/D3.js）
- 支持动画效果和交互
- 响应式设计，移动端友好

#### 3.2 依恋类型说明
**优先级：P1**

- **展示内容**：
  - 四种类型的特征对比
  - 典型行为模式举例
  - 关系中的表现形式
  - 发展建议摘要

### 4. 支付系统功能

#### 4.1 支付集成
**优先级：P0**

- **支付方式**：
  - Stripe 

**功能要求**：
- 支付流程简洁顺畅
- 支持多种货币（主要支持RMB）
- 支付状态实时跟踪
- 异常情况处理完善

#### 4.2 订单管理
**优先级：P1**

- **功能包括**：
  - 使用Stripe提供的后台进行管理，不用开发

### 5. 用户管理功能


#### 5.1 访客模式（推荐）
**优先级：P0**

- **功能包括**：
  - 无需注册即可测评
  - 本地存储测评结果

### 6. 数据安全功能

#### 6.1 隐私保护
**优先级：P0**

- **保护措施**：
  - 测评数据本地处理
  - 不存储用户答题内容
  - HTTPS全程加密传输
  - 符合GDPR等隐私法规

#### 6.2 数据备份
**优先级：P1**

- **备份策略**：
  - 本地浏览器存储
  - 云端备份（用户同意后）
  - 数据导出功能
  - 数据清除选项

## 🏗️ 技术架构设计

### 系统架构选择

#### 方案A：纯静态方案（推荐）
**适用场景**：快速上线，成本控制

```
前端：Vue.js/React + TypeScript
构建：Vite/Webpack
部署：GitHub Pages/Netlify/Vercel  
支付：Stripe Payment Links
存储：localStorage + URL参数传递
```

**优点**：
- 开发部署简单快速
- 运维成本极低
- 天然支持CDN加速
- 安全性高（无服务器攻击面）

**缺点**：
- 功能扩展受限
- 无法实现复杂用户管理
- 数据分析能力有限

#### 方案B：轻后端方案
**适用场景**：功能完整，数据分析

```
前端：Vue.js/React + TypeScript
后端：Node.js(Express) / Python(FastAPI) / Go(Gin)
数据库：PostgreSQL / MongoDB
部署：Docker + 云服务器
支付：Stripe API
存储：数据库 + Redis缓存
```

**优点**：
- 功能完整可扩展
- 支持用户系统和数据分析
- 更好的SEO和性能优化
- 便于A/B测试和运营

**缺点**：
- 开发周期较长
- 运维复杂度增加
- 成本相对较高

### 推荐技术栈

#### 前端技术栈
```javascript
// 核心框架
Vue 3 + TypeScript + Vite

// UI组件库
Element Plus / Ant Design Vue

// 图表库
Chart.js / ECharts

// 工具库
Lodash, Day.js, Axios

// 样式处理
Tailwind CSS / SCSS

// 状态管理  
Pinia (Vue) / Zustand (React)
```

#### 开发工具链
```javascript
// 代码质量
ESLint + Prettier + Husky

// 测试框架
Vitest + Vue Test Utils

// 构建优化
Vite Bundle Analyzer

// 部署
GitHub Actions / Vercel
```

### 数据模型设计

#### 核心数据结构

```typescript
// 测评问题模型
interface ECRQuestion {
  id: number;
  text: string;
  dimension: 'anxiety' | 'avoidance';
  reverse: boolean;
  order: number;
}

// 测评结果模型
interface AssessmentResult {
  id: string;
  timestamp: number;
  answers: number[];
  scores: {
    anxiety: number;
    avoidance: number;
  };
  attachmentType: 'secure' | 'anxious' | 'avoidant' | 'chaotic';
  completed: boolean;
}

// 报告数据模型
interface DetailedReport {
  id: string;
  userId?: string;
  assessmentId: string;
  reportType: 'basic' | 'detailed';
  content: {
    summary: string;
    analysis: string[];
    suggestions: string[];
    charts: ChartData[];
  };
  isPaid: boolean;
  createdAt: number;
}

// 支付订单模型
interface PaymentOrder {
  id: string;
  assessmentId: string;
  amount: number;
  currency: string;
  status: 'pending' | 'paid' | 'failed' | 'refunded';
  provider: 'stripe';
  providerId: string;
  createdAt: number;
  paidAt?: number;
}
```

## 🎨 UI/UX设计规范

### 设计原则
- **简洁性**：界面清晰，操作直观
- **专业性**：体现心理学测评的专业权威  
- **安全感**：让用户感觉隐私受到保护
- **包容性**：适合不同年龄和文化背景的用户

### 视觉设计规范

#### 色彩方案
```css
/* 主色调 - 专业可信赖 */
--primary-color: #667eea;      /* 主蓝色 */
--primary-dark: #5a6fd8;       /* 深蓝色 */
--primary-light: #8b9cf7;      /* 浅蓝色 */

/* 辅助色 - 温暖亲和 */
--secondary-color: #764ba2;    /* 紫色 */
--accent-color: #f093fb;       /* 粉色 */

/* 功能色 */
--success-color: #52c41a;      /* 成功绿 */
--warning-color: #faad14;      /* 警告橙 */
--error-color: #ff4d4f;        /* 错误红 */
--info-color: #1890ff;         /* 信息蓝 */

/* 中性色 */
--text-primary: #2c3e50;       /* 主文本 */
--text-secondary: #7f8c8d;     /* 次要文本 */
--border-color: #e8e8e8;       /* 边框色 */
--background: #f8f9fa;         /* 背景色 */
```

#### 字体规范
```css
/* 字体系列 */
--font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;

/* 字体大小 */
--font-size-h1: 2.5rem;        /* 标题1 */
--font-size-h2: 2rem;          /* 标题2 */  
--font-size-h3: 1.5rem;        /* 标题3 */
--font-size-body: 1rem;        /* 正文 */
--font-size-small: 0.875rem;   /* 小字 */

/* 行高 */
--line-height-tight: 1.25;
--line-height-normal: 1.5;
--line-height-loose: 1.75;
```

#### 间距规范
```css
/* 间距系统 - 8px基准 */
--spacing-xs: 0.25rem;     /* 4px */
--spacing-sm: 0.5rem;      /* 8px */
--spacing-md: 1rem;        /* 16px */
--spacing-lg: 1.5rem;      /* 24px */
--spacing-xl: 2rem;        /* 32px */
--spacing-2xl: 3rem;       /* 48px */
```

### 交互设计规范

#### 按钮设计
```css
/* 主要按钮 */
.btn-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

/* 次要按钮 */
.btn-secondary {
  background: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
  padding: 12px 24px;
  border-radius: 8px;
}
```

#### 表单设计
```css
/* 输入框 */
.form-input {
  padding: 12px 16px;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  font-size: var(--font-size-body);
  transition: border-color 0.3s ease;
}

.form-input:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}
```

### 响应式设计

#### 断点定义
```css
/* 移动设备 */
@media (max-width: 767px) { }

/* 平板设备 */  
@media (min-width: 768px) and (max-width: 1023px) { }

/* 桌面设备 */
@media (min-width: 1024px) { }

/* 大屏设备 */
@media (min-width: 1200px) { }
```

#### 移动端优化
- 触摸友好的按钮尺寸（最小44px）
- 合适的字体大小（最小16px）
- 简化的导航结构
- 优化的加载性能

## 📊 非功能性需求

### 性能要求

#### 页面性能
- **首屏加载时间**：< 2秒（3G网络）
- **页面交互响应**：< 100ms
- **测评完成流程**：< 30秒
- **报告生成时间**：< 5秒

#### 系统性能
- **并发用户**：支持1000+同时在线
- **数据处理**：单个测评结果计算 < 50ms
- **文件下载**：报告PDF生成 < 3秒
- **CDN覆盖**：全球主要地区 < 200ms延迟

### 可用性要求

#### 系统稳定性
- **正常运行时间**：99.9%以上
- **错误率**：< 0.1%
- **数据一致性**：100%
- **备份恢复**：RTO < 1小时，RPO < 15分钟

#### 用户体验
- **易用性评分**：SUS评分 > 80
- **任务完成率**：> 95%
- **用户满意度**：> 4.5/5.0
- **无障碍访问**：符合WCAG 2.1 AA标准

### 安全要求

#### 数据安全
- **传输加密**：TLS 1.3
- **数据存储**：端到端加密
- **访问控制**：RBAC权限管理
- **审计日志**：完整的操作记录

#### 隐私保护
- **数据最小化**：仅收集必要信息
- **用户控制**：数据删除和导出权利
- **合规性**：GDPR、CCPA等法规遵循
- **匿名化**：敏感数据去标识化处理

### 兼容性要求

#### 浏览器兼容
- **现代浏览器**：Chrome 90+, Firefox 90+, Safari 14+, Edge 90+
- **移动浏览器**：iOS Safari 14+, Android Chrome 90+
- **功能降级**：旧版浏览器基础功能可用

#### 设备兼容
- **屏幕尺寸**：320px - 2560px
- **操作系统**：Windows 10+, macOS 10.15+, iOS 14+, Android 8+
- **输入方式**：鼠标、触摸、键盘、语音

## 🚀 开发计划与里程碑

### 开发阶段规划

#### 第一阶段：核心功能开发（4-6周）
**目标**：完成MVP版本，支持基础测评和报告

**Sprint 1 (2周)**：
- 项目初始化和环境搭建
- 测评页面开发
- 基础计分算法实现
- 简单报告展示

**Sprint 2 (2周)**：
- 支付系统集成
- 详细报告生成
- 基础数据可视化
- 移动端适配

**关键里程碑**：
- ✅ 完整测评流程可用
- ✅ 支付功能正常
- ✅ 基础报告生成
- ✅ 移动端基本可用

#### 第二阶段：功能完善（3-4周）
**目标**：完善用户体验和系统稳定性

**Sprint 3 (2周)**：
- UI/UX优化
- 高级数据可视化
- 性能优化
- 错误处理完善

**Sprint 4 (2周)**：
- 用户反馈收集
- A/B测试实施
- 安全性加固
- 文档完善

**关键里程碑**：
- ✅ 用户体验优良
- ✅ 系统性能达标
- ✅ 安全测试通过
- ✅ 文档齐全

#### 第三阶段：上线运营（2-3周）
**目标**：正式上线并稳定运营

**Sprint 5 (1.5周)**：
- 生产环境部署
- 监控告警配置
- 备份策略实施
- 运营工具开发

**Sprint 6 (1.5周)**：
- 用户增长实验
- 数据分析配置
- 客服系统搭建
- 运营流程优化

**关键里程碑**：
- ✅ 生产环境稳定
- ✅ 监控体系完善
- ✅ 运营流程顺畅
- ✅ 用户增长良性

### 技术债务管理
- **代码质量**：保持85%+测试覆盖率
- **性能监控**：实时性能指标跟踪
- **安全审计**：每月安全漏洞扫描
- **依赖更新**：每季度依赖包更新

### 风险评估与应对

#### 技术风险
**风险1：第三方服务依赖**
- 影响：支付或图表库不可用
- 概率：中
- 应对：准备备选方案和服务降级

**风险2：性能瓶颈**
- 影响：用户体验下降
- 概率：中
- 应对：提前性能测试和优化

#### 业务风险
**风险1：用户接受度**
- 影响：产品市场契合度低
- 概率：中
- 应对：MVP快速验证和迭代

**风险2：竞争压力**
- 影响：市场份额流失
- 概率：高
- 应对：差异化定位和快速创新

## 📈 运营策略与商业模式

### 商业模式设计

#### 核心价值主张
- **专业权威**：基于学术权威的ECR量表
- **即时可得**：无需预约，随时在线测评
- **隐私安全**：本地处理，保护用户隐私
- **物超所值**：相比线下咨询，成本低效果好

#### 收入模式
```
基础测评: 免费
├── 获取基本依恋类型
├── 简单特征描述  
└── 引导付费升级

详细报告: ¥19.9
├── 深度心理分析
├── 个性化建议
├── 专业图表展示
└── PDF报告下载

增值服务: ¥39.9-99.9 (未来扩展)
├── 一对一咨询预约
├── 关系改善课程
├── 伴侣匹配建议
└── 长期跟踪服务
```

#### 定价策略
- **心理锚定**：显示原价¥39.9，现价¥19.9
- **价值感知**：对比线下咨询¥200-500/小时
- **支付便利**：支持多种支付方式
- **退款保障**：7天无理由退款

### 用户获取策略

#### 内容营销
- **SEO优化**：依恋类型、心理测试等关键词
- **内容输出**：心理学科普文章和视频
- **社交媒体**：微博、小红书、知乎运营
- **KOL合作**：心理学博主和情感专家

#### 渠道推广
- **搜索引擎**：Google Ads、百度推广
- **社交平台**：微信朋友圈、微博广告
- **合作推广**：心理咨询机构、教育平台
- **口碑传播**：朋友推荐、社群分享

#### 转化优化
- **免费体验**：降低用户尝试门槛
- **社会认同**：显示使用人数和好评
- **紧迫感**：限时优惠和稀缺性暗示
- **信任建立**：专业资质和安全保障

### 用户留存策略

#### 产品留存
- **报告收藏**：支持随时查看历史报告
- **分享功能**：便于用户分享给朋友
- **再测提醒**：建议6个月后重新测评
- **内容推荐**：相关心理学文章和工具

#### 关系营销
- **邮件营销**：定期发送心理健康贴士
- **社群运营**：建立用户交流群组
- **线下活动**：心理学讲座和工作坊
- **客户服务**：及时响应用户问题

### 数据分析体系

#### 关键指标
```
用户获取指标:
- DAU/MAU: 日活/月活用户数
- UV/PV: 独立访客/页面浏览量
- 转化率: 访客->注册->付费转化率
- 获客成本: CAC (Customer Acquisition Cost)

用户行为指标:
- 测评完成率: 开始测评->完成测评
- 支付转化率: 免费报告->付费报告  
- 报告下载率: 付费->下载PDF
- 用户满意度: NPS净推荐值

商业指标:
- 营收: MRR月度经常性收入
- 客单价: ARPU平均每用户收入
- 投资回报: ROI/ROAS
- 用户生命周期价值: LTV
```

#### 分析工具
- **网站分析**：Google Analytics 4
- **用户行为**：Hotjar热力图分析
- **A/B测试**：Optimizely或自建
- **商业智能**：自建Dashboard

## 🔒 合规与风险管理

### 法律合规要求

#### 医疗器械监管
- **免责声明**：明确说明非医疗诊断工具
- **使用限制**：18岁以上成年人使用
- **结果解释**：避免医疗化表述
- **专业建议**：建议咨询专业人士

#### 隐私法规遵循
- **GDPR合规**：欧盟用户数据保护
- **CCPA合规**：加州消费者隐私法案
- **个保法合规**：中国个人信息保护法
- **数据跨境**：符合数据出境安全评估

#### 消费者权益保护
- **价格透明**：明确标示所有费用
- **服务承诺**：明确服务内容和质量标准
- **退款政策**：7天无理由退款
- **争议解决**：建立客诉处理机制

### 内容风险管控

#### 心理测评风险
- **专业性审核**：心理学专家内容审核
- **准确性保证**：定期校验算法准确性
- **适用性说明**：明确适用人群和限制
- **危机干预**：识别高风险用户并提供资源

#### 商业风险管控
- **支付安全**：PCI DSS合规，防止支付欺诈
- **数据泄露**：建立数据泄露应急响应预案
- **服务中断**：多重备份和灾难恢复机制
- **知识产权**：确保不侵犯第三方版权

### 质量保证体系

#### 代码质量
- **代码审查**：所有代码变更必须经过同行评审
- **自动化测试**：单元测试、集成测试、端到端测试
- **静态分析**：代码质量和安全漏洞扫描
- **性能测试**：定期进行负载和压力测试

#### 内容质量
- **专业审核**：心理学专家参与内容制作和审核
- **用户测试**：定期进行可用性测试
- **反馈收集**：建立用户反馈收集和处理机制
- **持续改进**：基于数据和反馈持续优化

## 📚 附录

### 参考文献
1. Brennan, K. A., Clark, C. L., & Shaver, P. R. (1998). Self-report measurement of adult attachment: An integrative overview.
2. Hazan, C., & Shaver, P. (1987). Romantic love conceptualized as an attachment process.
3. Bowlby, J. (1988). A secure base: Parent-child attachment and healthy human development.

### 专业术语表
- **ECR**: Experiences in Close Relationships，亲密关系经历量表
- **依恋理论**: Attachment Theory，解释人际关系形成的心理学理论
- **李克特量表**: Likert Scale，态度测量的心理计量方法
- **依恋焦虑**: Attachment Anxiety，对关系丧失的担忧程度
- **依恋回避**: Attachment Avoidance，对亲密关系的回避程度

### 竞品分析
1. **16Personalities**: 免费+付费模式，用户体验良好，但非专业心理测评
2. **Psychology Today**: 专业性强，但界面陈旧，用户体验一般
3. **国内心理测评平台**: 壹心理、简单心理等，功能相对简单

### 技术选型对比

#### 前端框架对比
| 框架 | 优点 | 缺点 | 适用场景 |
|------|------|------|----------|
| Vue 3 | 学习成本低，生态完善 | 大型项目支持一般 | 中小型项目 |
| React | 生态丰富，社区活跃 | 学习成本较高 | 大型复杂项目 |
| Angular | 功能完整，企业级 | 学习成本很高，体积大 | 大型企业应用 |

#### 后端框架对比
| 框架 | 优点 | 缺点 | 适用场景 |
|------|------|------|----------|
| Node.js | JavaScript统一，生态丰富 | CPU密集任务性能一般 | 快速开发，I/O密集 |
| Python | 开发效率高，AI集成好 | 性能相对较低 | 数据分析，AI应用 |
| Go | 性能优秀，并发能力强 | 生态相对较小 | 高性能服务 |

---

**文档版本**: v1.0  
**创建日期**: 2024年1月  
**审核状态**: 待审核  
**维护负责人**: 产品团队

> 本文档将根据项目进展和市场变化持续更新，确保需求的准确性和时效性。