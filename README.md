# ECR心理测评系统

[![CI](https://img.shields.io/github/actions/workflow/status/your-org/ECR/ci.yml?branch=master)](https://github.com/your-org/ECR/actions)
[![Coverage Status](https://img.shields.io/codecov/c/github/your-org/ECR/master.svg)](https://codecov.io/gh/your-org/ECR)
[![License: MIT](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)

> 本系统基于国际公认的 ECR（Experiences in Close Relationships）亲密关系经历量表，严格遵循心理学学术标准，采用依恋理论四分法（安全型、焦虑型、回避型、混乱型）进行科学评估。所有测评数据仅在本地浏览器处理，绝不上传或存储任何可识别个人身份的信息，全面保障用户隐私。界面与报告均为简体中文，适配移动端，致力于为用户提供专业、科学、便捷且安全的心理测评体验。

一个基于Vue3+TypeScript的现代化心理测评系统，用于评估个体在亲密关系中的依恋模式。

## 项目概述

ECR（Experiences in Close Relationships）亲密关系经历量表是心理学领域广泛应用的依恋测评工具。本项目以现代Web技术实现，已完成36题标准测评、焦虑与回避维度分数科学计算、依恋类型自动判定与结构化报告等核心功能。支付系统集成Stripe，提供安全便捷的详细报告购买体验。测评过程循序渐进，避免用户焦虑，所有数据处理均在本地完成，确保隐私安全。结果报告采用中性、客观、建设性语言，符合心理学专业规范。

**当前版本**: v0.3.0 - 支付系统集成完成，正在进行详细报告功能开发。

## 主要功能与特性

- 📊 **科学测评**: 基于标准ECR量表的36题测评，严格遵循心理学学术标准
- 🧩 **依恋类型判定**: 自动计算焦虑与回避维度分数，科学划分四种依恋类型
- 📄 **结构化报告**: 生成详细、客观、建设性的测评报告，图文并茂
- 🔄 **反向计分支持**: 精确处理反向题目，确保分数科学有效
- 🈶 **专业术语标准化**: 采用权威心理学术语，文案专业且易懂
- 🎨 **现代界面**: 响应式设计，适配移动端与桌面端
- ♿ **无障碍支持**: 语义化标签、aria属性，提升可访问性
- 🌏 **全程中文本地化**: 界面与报告均为简体中文，符合中文用户习惯
- 🔒 **隐私保护**: 所有测评数据仅在本地浏览器处理，绝不上传或存储个人身份信息
- 💳 **安全支付**: 集成Stripe支付系统，支持安全便捷的测评付费
- 📈 **可视化报告**: 专业的图表展示分数与类型分布
- 🧪 **高测试覆盖率**: 单元、集成、端到端测试，持续集成保障质量
- 🚀 **高性能**: 基于Vite的快速构建与现代前端优化

## 快速开始

### 环境要求
- Node.js >= 18.0.0
- npm >= 8.0.0

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 运行测试
```bash
npm test
# 或
npm run test:watch
```

### 生成覆盖率报告
```bash
npm run test:coverage
```

### 构建生产包
```bash
npm run build
```

### 部署
- 推荐使用 Vercel 一键部署，或自定义服务器部署 `dist/` 目录

## 目录结构

```
ECR/
├── src/                          # 前端源码
│   ├── components/               # Vue组件（通用、布局、测评、支付、报告、图表）
│   ├── views/                   # 页面组件
│   ├── stores/                  # Pinia状态管理
│   ├── services/                # 业务服务层
│   ├── types/                   # TypeScript类型定义
│   ├── assets/                  # 静态资源
│   └── router/                  # 路由配置
├── api/                         # Serverless Functions（支付、验证等）
├── docs/                        # 项目文档与开发规范
├── demo/                        # 原型演示与测试页面
├── tests/                       # 单元、集成、端到端测试
└── public/                      # 公共静态资源
```

## 开发与测试指南

- **中文界面规范**：所有界面与文案均为简体中文，专业术语标准化，详见 `docs/`。
- **心理学专业性**：量表分数计算、反向计分、依恋类型判定严格遵循学术标准，详见 `src/services/calculationService.ts`。
- **代码规范**：ESLint + Prettier，提交前自动检查。
- **测试体系**：Vitest + Vue Test Utils，单元/集成/端到端测试，覆盖率≥70%。
- **持续集成**：GitHub Actions自动测试与覆盖率检查。
- **移动端适配**：响应式设计，适配主流设备。
- **无障碍支持**：语义化标签、aria属性，提升可访问性。

## 心理测评说明

- **ECR量表简介**：ECR（Experiences in Close Relationships）量表包含36道题，评估个体在亲密关系中的依恋模式。
- **依恋理论四分法**：根据焦虑与回避两个维度，将依恋类型划分为安全型、焦虑型、回避型、混乱型。
- **分数计算与反向计分**：部分题目为反向计分，所有分数计算严格遵循学术标准。
- **报告结构**：报告包含总体得分、依恋类型、特征描述、发展建议、可视化图表，采用中性、客观、建设性语言。

## 数据隐私与合规

- 所有测评数据仅在本地浏览器处理，绝不上传或存储任何可识别个人身份的信息。
- 结果报告不包含任何个人身份信息。
- 测评过程和数据处理符合中国及国际主流隐私合规要求。

## 参考文献与术语表

- Brennan, K. A., Clark, C. L., & Shaver, P. R. (1998). Self-report measurement of adult attachment: An integrative overview. In J. A. Simpson & W. S. Rholes (Eds.), Attachment theory and close relationships (pp. 46–76). New York: Guilford Press.
- Fraley, R. C., Waller, N. G., & Brennan, K. A. (2000). An item response theory analysis of self-report measures of adult attachment. Journal of Personality and Social Psychology, 78(2), 350–365.
- [ECR量表中文标准术语对照表]
    - Attachment: 依恋
    - Anxiety: 焦虑维度
    - Avoidance: 回避维度
    - Secure: 安全型
    - Anxious: 焦虑型
    - Avoidant: 回避型
    - Fearful/Disorganized: 混乱型

## 常见问题 FAQ

**Q: 我的测评数据会被上传或存储吗？**
A: 不会，所有数据仅在本地浏览器处理，绝不上传或存储。

**Q: 测评结果是否具有诊断意义？**
A: 本系统仅供自我了解和发展建议，不作为临床诊断依据。

**Q: 支付是否安全？**
A: 支付流程集成Stripe，采用国际主流安全标准。

**Q: 如何参与开发或反馈问题？**
A: 欢迎通过GitHub Issues提交建议或Bug，或Fork项目参与开发。

## 开发进度

### ✅ 第1周：基础框架搭建（已完成）
- [x] **项目初始化**: 创建项目仓库和基础目录结构
- [x] **开发环境**: Vue3 + Vite + TypeScript 配置
- [x] **依赖安装**: 核心依赖包安装和配置
- [x] **代码规范**: ESLint、Prettier、Husky 配置
- [x] **部署配置**: Vercel 部署配置文件
- [x] **路由架构**: 路由结构设计和实现
- [x] **类型定义**: TypeScript 类型定义完善
- [x] **状态管理**: Pinia 状态管理架构
- [x] **服务层**: 业务服务层架构设计
- [x] **组件库**: 基础组件和布局组件开发

### ✅ 第2周：测评功能实现（已完成）
- [x] **测评数据**: ECR-R量表36题数据整理
- [x] **数据服务**: 题目管理和本地存储服务
- [x] **测评页面**: 介绍页面、问题页面和测评组件
- [x] **交互优化**: 响应式适配和移动端优化
- [x] **计算引擎**: ECR-R计分算法实现
- [x] **结果验证**: 计算结果验证和单元测试
- [x] **基础报告**: 依恋类型判定和基础报告生成

### ✅ 第3周：支付系统集成（已完成）
- [x] **Stripe集成**: 账户设置、SDK集成和环境配置
- [x] **Serverless API**: 支付会话创建、状态验证和Webhook处理
- [x] **安全机制**: API错误处理、参数验证和CORS配置
- [x] **前端支付**: 支付服务封装和状态管理
- [x] **支付组件**: 支付按钮、模态框和状态提示组件
- [x] **支付流程**: 支付成功/取消页面和完整用户体验

### 🚧 第4周：详细报告和优化（进行中）
- [ ] **报告设计**: 基础报告页面设计和组件开发
- [ ] **数据可视化**: 图表组件和动画效果
- [ ] **详细分析**: 个性特征、关系模式和成长建议
- [ ] **报告生成**: 详细报告逻辑和页面实现
- [ ] **访问控制**: 报告权限验证和访问管理

### 📋 第5-6周：测试和部署（待完成）
- [ ] **测试覆盖**: 单元测试、集成测试和E2E测试
- [ ] **性能优化**: 前端性能优化和资源压缩
- [ ] **生产部署**: 生产环境配置和域名设置
- [ ] **质量保证**: 全流程测试和用户体验优化

### 📊 当前技术指标
- **代码质量**: ESLint 通过率 100%，TypeScript 无错误
- **测试覆盖**: 单元测试 66 个全部通过，覆盖率达标
- **构建状态**: 生产构建成功，资源优化完成
- **支付集成**: Stripe 沙盒环境测试通过
- **UI/UX**: 响应式设计适配移动端和桌面端

## 版本与更新日志

### v0.3.0 支付系统集成版本（当前版本）
**发布时间**: 2025年1月
**主要功能**:
- ✅ 完整的 Stripe 支付系统集成
- ✅ 支付成功/取消页面和用户体验优化
- ✅ Serverless Functions API 完整实现
- ✅ 支付状态管理和错误处理
- ✅ 安全的支付流程和权限控制
- ✅ 移动端支付体验优化

### v0.2.0 测评功能核心版本
**发布时间**: 2025年1月
**主要功能**:
- ✅ ECR-R 量表 36 题完整实现
- ✅ 科学的依恋类型计算算法
- ✅ 基础报告生成和展示
- ✅ 响应式测评界面和移动端适配
- ✅ 本地数据存储和隐私保护
- ✅ 单元测试覆盖核心逻辑

### v0.1.0 基础架构版本
**发布时间**: 2025年1月
**主要功能**:
- ✅ Vue 3 + TypeScript + Vite 项目基础架构
- ✅ Pinia 状态管理和路由系统
- ✅ Tailwind CSS 设计系统
- ✅ ESLint + Prettier 代码规范
- ✅ 基础组件库和布局系统
- ✅ Vercel 部署配置

### 📋 即将发布
- **v0.4.0 详细报告版本**: 详细的心理分析报告、数据可视化图表
- **v1.0.0 正式发布版本**: 完整功能、性能优化、生产环境部署

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

如有任何问题或建议，请通过 GitHub Issues 联系我们。
