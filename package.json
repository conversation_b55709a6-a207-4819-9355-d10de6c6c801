{"name": "ecr-psychology-assessment", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "vite build", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build --force", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "dev": "vite", "prepare": "husky install", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch"}, "dependencies": {"@stripe/stripe-js": "^7.5.0", "@vueuse/core": "^10.7.0", "chart.js": "^4.5.0", "pinia": "^2.1.7", "stripe": "^18.3.0", "vue": "^3.4.0", "vue-chartjs": "^5.3.2", "vue-router": "^4.2.5"}, "devDependencies": {"@rushstack/eslint-patch": "^1.3.3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/vue": "^8.1.0", "@tsconfig/node18": "^18.2.2", "@types/node": "^18.19.3", "@types/stripe": "^8.0.416", "@vitejs/plugin-vue": "^4.5.2", "@vitest/coverage-v8": "^3.2.4", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.5.1", "autoprefixer": "^10.4.16", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "husky": "^8.0.3", "jsdom": "^26.1.0", "lint-staged": "^15.2.0", "msw": "^2.10.4", "npm-run-all2": "^6.1.1", "postcss": "^8.4.32", "prettier": "^3.0.3", "tailwindcss": "^3.4.0", "typescript": "~5.3.0", "vite": "^5.0.10", "vitest": "^3.2.4", "vue-tsc": "^1.8.25"}, "lint-staged": {"*.{js,ts,vue}": ["eslint --fix", "prettier --write"]}}